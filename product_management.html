<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - إضافة منتج جديد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .dynamic-list {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: white;
        }
        .dynamic-item {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .dynamic-item input {
            flex: 1;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .variants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .variant-item {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            padding: 10px;
            background: #d4edda;
            border-radius: 4px;
            margin: 10px 0;
        }
        .quick-add {
            display: flex;
            gap: 10px;
            align-items: end;
            margin-bottom: 15px;
        }
        .quick-add input {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>إضافة منتج جديد</h1>
        
        <div id="loading" class="loading" style="display: none;">جاري التحميل...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="success" class="success" style="display: none;"></div>

        <form id="productForm">
            <!-- بيانات المنتج الأساسية -->
            <div class="section">
                <h3>بيانات المنتج الأساسية</h3>
                <div class="form-group">
                    <label>اسم المنتج *</label>
                    <input type="text" name="product[name]" required>
                </div>
                <div class="form-group">
                    <label>وصف المنتج</label>
                    <textarea name="product[description]"></textarea>
                </div>
                <div class="form-group">
                    <label>السعر *</label>
                    <input type="number" name="product[price]" step="0.01" min="0" required>
                </div>
                <div class="form-group">
                    <label>التقييم</label>
                    <input type="number" name="product[rating]" step="0.1" min="0" max="5">
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" name="product[is_active]" id="is_active" checked>
                    <label for="is_active">منتج نشط</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" name="product[is_trending]" id="is_trending">
                    <label for="is_trending">منتج رائج</label>
                </div>
            </div>

            <!-- الفئة -->
            <div class="section">
                <h3>الفئة</h3>
                <div class="quick-add">
                    <input type="text" id="newCategoryName" placeholder="اسم فئة جديدة">
                    <input type="text" id="newCategoryDesc" placeholder="وصف الفئة (اختياري)">
                    <button type="button" class="btn btn-secondary" onclick="quickAddCategory()">إضافة فئة</button>
                </div>
                <div class="form-group">
                    <label>اختر فئة موجودة أو أدخل اسم فئة جديدة</label>
                    <select id="categorySelect" name="category[id]">
                        <option value="">-- اختر فئة موجودة --</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>أو أدخل اسم فئة جديدة</label>
                    <input type="text" name="category[name]" placeholder="اسم الفئة الجديدة">
                </div>
                <div class="form-group">
                    <label>وصف الفئة</label>
                    <input type="text" name="category[description]" placeholder="وصف الفئة">
                </div>
            </div>

            <!-- الألوان -->
            <div class="section">
                <h3>الألوان</h3>
                <div class="quick-add">
                    <input type="text" id="newColorName" placeholder="اسم لون جديد">
                    <input type="text" id="newColorHex" placeholder="#FF0000 (اختياري)">
                    <button type="button" class="btn btn-secondary" onclick="quickAddColor()">إضافة لون</button>
                </div>
                <div id="colorsContainer" class="dynamic-list">
                    <div class="dynamic-item">
                        <select name="colors[0][id]">
                            <option value="">-- اختر لون موجود --</option>
                        </select>
                        <input type="text" name="colors[0][name]" placeholder="أو اسم لون جديد">
                        <input type="text" name="colors[0][hex_code]" placeholder="#FF0000">
                        <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
                    </div>
                </div>
                <button type="button" class="btn btn-success" onclick="addColor()">إضافة لون آخر</button>
            </div>

            <!-- الأحجام -->
            <div class="section">
                <h3>الأحجام</h3>
                <div class="quick-add">
                    <input type="text" id="newSizeName" placeholder="اسم حجم جديد">
                    <input type="text" id="newSizeAbbr" placeholder="S, M, L (اختياري)">
                    <button type="button" class="btn btn-secondary" onclick="quickAddSize()">إضافة حجم</button>
                </div>
                <div id="sizesContainer" class="dynamic-list">
                    <div class="dynamic-item">
                        <select name="sizes[0][id]">
                            <option value="">-- اختر حجم موجود --</option>
                        </select>
                        <input type="text" name="sizes[0][name]" placeholder="أو اسم حجم جديد">
                        <input type="text" name="sizes[0][abbreviation]" placeholder="S, M, L">
                        <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
                    </div>
                </div>
                <button type="button" class="btn btn-success" onclick="addSize()">إضافة حجم آخر</button>
            </div>

            <!-- التاغات -->
            <div class="section">
                <h3>التاغات (اختياري)</h3>
                <div class="quick-add">
                    <input type="text" id="newTagName" placeholder="اسم تاغ جديد">
                    <button type="button" class="btn btn-secondary" onclick="quickAddTag()">إضافة تاغ</button>
                </div>
                <div id="tagsContainer" class="dynamic-list">
                    <div class="dynamic-item">
                        <select name="tags[0][id]">
                            <option value="">-- اختر تاغ موجود --</option>
                        </select>
                        <input type="text" name="tags[0][name]" placeholder="أو اسم تاغ جديد">
                        <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
                    </div>
                </div>
                <button type="button" class="btn btn-success" onclick="addTag()">إضافة تاغ آخر</button>
            </div>

            <!-- التوليفات -->
            <div class="section">
                <h3>التوليفات (اللون + الحجم + الكمية)</h3>
                <p>سيتم إنشاء التوليفات تلقائياً بناءً على الألوان والأحجام المحددة أعلاه</p>
                <div id="variantsContainer" class="variants-grid">
                    <!-- سيتم ملؤها تلقائياً -->
                </div>
                <button type="button" class="btn btn-primary" onclick="generateVariants()">إنشاء التوليفات</button>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-success" style="font-size: 16px; padding: 15px 30px;">
                    إنشاء المنتج
                </button>
            </div>
        </form>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let formData = {};

        // تحميل البيانات عند فتح الصفحة
        window.onload = async () => {
            await loadFormData();
        };

        // تحميل بيانات النموذج
        async function loadFormData() {
            try {
                const response = await fetch(`${API_BASE}/product-management/form-data`);
                formData = await response.json();
                
                populateSelects();
            } catch (error) {
                showError('خطأ في تحميل البيانات: ' + error.message);
            }
        }

        // ملء القوائم المنسدلة
        function populateSelects() {
            // الفئات
            const categorySelect = document.getElementById('categorySelect');
            formData.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });

            // الألوان
            document.querySelectorAll('select[name*="colors"][name*="[id]"]').forEach(select => {
                formData.colors.forEach(color => {
                    const option = document.createElement('option');
                    option.value = color.id;
                    option.textContent = color.name;
                    select.appendChild(option);
                });
            });

            // الأحجام
            document.querySelectorAll('select[name*="sizes"][name*="[id]"]').forEach(select => {
                formData.sizes.forEach(size => {
                    const option = document.createElement('option');
                    option.value = size.id;
                    option.textContent = size.name;
                    select.appendChild(option);
                });
            });

            // التاغات
            document.querySelectorAll('select[name*="tags"][name*="[id]"]').forEach(select => {
                formData.tags.forEach(tag => {
                    const option = document.createElement('option');
                    option.value = tag.id;
                    option.textContent = tag.name;
                    select.appendChild(option);
                });
            });
        }

        // إضافة عناصر جديدة
        function addColor() {
            const container = document.getElementById('colorsContainer');
            const index = container.children.length;
            const div = document.createElement('div');
            div.className = 'dynamic-item';
            div.innerHTML = `
                <select name="colors[${index}][id]">
                    <option value="">-- اختر لون موجود --</option>
                    ${formData.colors.map(color => `<option value="${color.id}">${color.name}</option>`).join('')}
                </select>
                <input type="text" name="colors[${index}][name]" placeholder="أو اسم لون جديد">
                <input type="text" name="colors[${index}][hex_code]" placeholder="#FF0000">
                <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
            `;
            container.appendChild(div);
        }

        function addSize() {
            const container = document.getElementById('sizesContainer');
            const index = container.children.length;
            const div = document.createElement('div');
            div.className = 'dynamic-item';
            div.innerHTML = `
                <select name="sizes[${index}][id]">
                    <option value="">-- اختر حجم موجود --</option>
                    ${formData.sizes.map(size => `<option value="${size.id}">${size.name}</option>`).join('')}
                </select>
                <input type="text" name="sizes[${index}][name]" placeholder="أو اسم حجم جديد">
                <input type="text" name="sizes[${index}][abbreviation]" placeholder="S, M, L">
                <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
            `;
            container.appendChild(div);
        }

        function addTag() {
            const container = document.getElementById('tagsContainer');
            const index = container.children.length;
            const div = document.createElement('div');
            div.className = 'dynamic-item';
            div.innerHTML = `
                <select name="tags[${index}][id]">
                    <option value="">-- اختر تاغ موجود --</option>
                    ${formData.tags.map(tag => `<option value="${tag.id}">${tag.name}</option>`).join('')}
                </select>
                <input type="text" name="tags[${index}][name]" placeholder="أو اسم تاغ جديد">
                <button type="button" class="btn btn-danger" onclick="removeItem(this)">حذف</button>
            `;
            container.appendChild(div);
        }

        function removeItem(button) {
            button.parentElement.remove();
        }

        // إضافة سريعة للعناصر
        async function quickAddCategory() {
            const name = document.getElementById('newCategoryName').value;
            const description = document.getElementById('newCategoryDesc').value;
            
            if (!name) {
                showError('يرجى إدخال اسم الفئة');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/product-management/quick-add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'category',
                        name: name,
                        extra_data: { description: description }
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    showSuccess('تم إضافة الفئة بنجاح');
                    document.getElementById('newCategoryName').value = '';
                    document.getElementById('newCategoryDesc').value = '';
                    await loadFormData(); // إعادة تحميل البيانات
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('خطأ في إضافة الفئة: ' + error.message);
            }
        }

        async function quickAddColor() {
            const name = document.getElementById('newColorName').value;
            const hex_code = document.getElementById('newColorHex').value;
            
            if (!name) {
                showError('يرجى إدخال اسم اللون');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/product-management/quick-add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'color',
                        name: name,
                        extra_data: { hex_code: hex_code }
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    showSuccess('تم إضافة اللون بنجاح');
                    document.getElementById('newColorName').value = '';
                    document.getElementById('newColorHex').value = '';
                    await loadFormData();
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('خطأ في إضافة اللون: ' + error.message);
            }
        }

        async function quickAddSize() {
            const name = document.getElementById('newSizeName').value;
            const abbreviation = document.getElementById('newSizeAbbr').value;
            
            if (!name) {
                showError('يرجى إدخال اسم الحجم');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/product-management/quick-add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'size',
                        name: name,
                        extra_data: { abbreviation: abbreviation }
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    showSuccess('تم إضافة الحجم بنجاح');
                    document.getElementById('newSizeName').value = '';
                    document.getElementById('newSizeAbbr').value = '';
                    await loadFormData();
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('خطأ في إضافة الحجم: ' + error.message);
            }
        }

        async function quickAddTag() {
            const name = document.getElementById('newTagName').value;
            
            if (!name) {
                showError('يرجى إدخال اسم التاغ');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/product-management/quick-add`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'tag',
                        name: name
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    showSuccess('تم إضافة التاغ بنجاح');
                    document.getElementById('newTagName').value = '';
                    await loadFormData();
                } else {
                    showError(result.message);
                }
            } catch (error) {
                showError('خطأ في إضافة التاغ: ' + error.message);
            }
        }

        // إنشاء التوليفات
        function generateVariants() {
            const colorsContainer = document.getElementById('colorsContainer');
            const sizesContainer = document.getElementById('sizesContainer');
            const variantsContainer = document.getElementById('variantsContainer');

            const colors = Array.from(colorsContainer.children).map((item, index) => {
                const select = item.querySelector('select');
                const nameInput = item.querySelector('input[name*="[name]"]');
                return {
                    index: index,
                    name: select.value ? formData.colors.find(c => c.id == select.value)?.name : nameInput.value
                };
            }).filter(c => c.name);

            const sizes = Array.from(sizesContainer.children).map((item, index) => {
                const select = item.querySelector('select');
                const nameInput = item.querySelector('input[name*="[name]"]');
                return {
                    index: index,
                    name: select.value ? formData.sizes.find(s => s.id == select.value)?.name : nameInput.value
                };
            }).filter(s => s.name);

            if (colors.length === 0 || sizes.length === 0) {
                showError('يرجى إضافة لون واحد على الأقل وحجم واحد على الأقل');
                return;
            }

            variantsContainer.innerHTML = '';
            let variantIndex = 0;

            colors.forEach(color => {
                sizes.forEach(size => {
                    const div = document.createElement('div');
                    div.className = 'variant-item';
                    div.innerHTML = `
                        <h4>${color.name} - ${size.name}</h4>
                        <input type="hidden" name="variants[${variantIndex}][color_index]" value="${color.index}">
                        <input type="hidden" name="variants[${variantIndex}][size_index]" value="${size.index}">
                        <label>الكمية:</label>
                        <input type="number" name="variants[${variantIndex}][quantity]" min="0" value="0" required>
                        <label>صورة (اختياري):</label>
                        <input type="text" name="variants[${variantIndex}][image]" placeholder="رابط الصورة">
                    `;
                    variantsContainer.appendChild(div);
                    variantIndex++;
                });
            });

            showSuccess('تم إنشاء التوليفات بنجاح');
        }

        // إرسال النموذج
        document.getElementById('productForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const success = document.getElementById('success');

            loading.style.display = 'block';
            error.style.display = 'none';
            success.style.display = 'none';

            try {
                const formData = new FormData(e.target);
                const data = {};

                // تحويل FormData إلى JSON
                for (let [key, value] of formData.entries()) {
                    if (key.includes('[') && key.includes(']')) {
                        const keys = key.replace(/\]/g, '').split('[');
                        let current = data;
                        
                        for (let i = 0; i < keys.length - 1; i++) {
                            if (!current[keys[i]]) {
                                current[keys[i]] = isNaN(keys[i + 1]) ? {} : [];
                            }
                            current = current[keys[i]];
                        }
                        
                        const lastKey = keys[keys.length - 1];
                        if (value !== '') {
                            current[lastKey] = value;
                        }
                    } else {
                        data[key] = value;
                    }
                }

                // تحويل checkboxes
                data.product.is_active = document.querySelector('input[name="product[is_active]"]').checked;
                data.product.is_trending = document.querySelector('input[name="product[is_trending]"]').checked;

                const response = await fetch(`${API_BASE}/product-management/create-complete`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                loading.style.display = 'none';

                if (response.ok) {
                    showSuccess('تم إنشاء المنتج بنجاح!');
                    e.target.reset();
                    document.getElementById('variantsContainer').innerHTML = '';
                } else {
                    showError(result.message || 'حدث خطأ أثناء إنشاء المنتج');
                }

            } catch (error) {
                loading.style.display = 'none';
                showError('خطأ في الشبكة: ' + error.message);
            }
        });

        // دوال المساعدة
        function showError(message) {
            const error = document.getElementById('error');
            error.textContent = message;
            error.style.display = 'block';
            setTimeout(() => error.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            const success = document.getElementById('success');
            success.textContent = message;
            success.style.display = 'block';
            setTimeout(() => success.style.display = 'none', 3000);
        }
    </script>
</body>
</html>
