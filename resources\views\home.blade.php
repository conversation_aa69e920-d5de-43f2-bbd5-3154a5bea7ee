@extends('layouts.main')

@section('title', 'E-Commerce Store - Quality Products at Great Prices')

@section('content')
<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">Discover Amazing Products</h1>
                <p class="hero-subtitle">Shop from our curated collection of quality products at unbeatable prices. Fast shipping, easy returns, and excellent customer service.</p>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">{{ $stats['total_products'] }}+</span>
                        <span class="stat-label">Products</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">{{ $stats['total_categories'] }}+</span>
                        <span class="stat-label">Categories</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">{{ $stats['total_variants'] }}+</span>
                        <span class="stat-label">Variants</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="{{ url('/products') }}" class="btn btn-primary">🛍️ Shop Now</a>
                    <a href="{{ url('/categories') }}" class="btn btn-outline">📂 Browse Categories</a>
                </div>
            </div>
            <div class="hero-image">
                <div class="hero-graphic">
                    <div class="floating-card">
                        <div class="card-icon">🛍️</div>
                        <div class="card-text">Quality Products</div>
                    </div>
                    <div class="floating-card">
                        <div class="card-icon">🚚</div>
                        <div class="card-text">Fast Shipping</div>
                    </div>
                    <div class="floating-card">
                        <div class="card-icon">💯</div>
                        <div class="card-text">Best Prices</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
@if($categories->count() > 0)
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Shop by Category</h2>
            <p class="section-subtitle">Explore our wide range of product categories</p>
        </div>
        <div class="categories-grid">
            @foreach($categories as $category)
                <a href="{{ url('/products?category=' . $category->id) }}" class="category-card">
                    <div class="category-icon">📂</div>
                    <h3 class="category-name">{{ $category->name }}</h3>
                    <p class="category-count">{{ $category->product_groups_count }} products</p>
                </a>
            @endforeach
        </div>
        <div class="section-footer">
            <a href="{{ url('/categories') }}" class="btn btn-outline">View All Categories →</a>
        </div>
    </div>
</section>
@endif

<!-- Featured Products Section -->
@if($featuredProducts->count() > 0)
<section class="featured-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">🔥 Trending Products</h2>
            <p class="section-subtitle">Check out our most popular items</p>
        </div>
        <div class="products-grid">
            @foreach($featuredProducts as $product)
                <div class="product-card">
                    <div class="product-image">
                        @if($product->main_image)
                            <img src="{{ $product->main_image }}" alt="{{ $product->name }}" loading="lazy">
                        @else
                            <div class="no-image">📦</div>
                        @endif
                        <div class="product-badges">
                            @if($product->is_trending)
                                <span class="badge trending">🔥 Trending</span>
                            @endif
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">{{ $product->category->name }}</div>
                        <h3 class="product-name">{{ $product->name }}</h3>
                        <p class="product-description">{{ Str::limit($product->description, 80) }}</p>
                        <div class="product-meta">
                            <div class="product-price">
                                <span class="price">${{ number_format($product->base_price, 2) }}</span>
                                @if($product->products->count() > 1)
                                    <span class="price-range">
                                        - ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
                                    </span>
                                @endif
                            </div>
                            @if($product->rating)
                                <div class="product-rating">
                                    @for($i = 1; $i <= 5; $i++)
                                        <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                                    @endfor
                                    <span class="rating-text">({{ number_format($product->rating, 1) }})</span>
                                </div>
                            @endif
                        </div>
                        <div class="product-variants">
                            @if($product->products->count() > 0)
                                <span class="variants-count">{{ $product->products->count() }} variants available</span>
                            @endif
                        </div>
                        <div class="product-actions">
                            <a href="{{ url('/products/' . $product->id) }}" class="btn btn-primary btn-sm">View Details</a>
                            <button class="btn btn-outline btn-sm" onclick="addToWishlist({{ $product->id }})">❤️</button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Latest Products Section -->
@if($latestProducts->count() > 0)
<section class="latest-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">✨ Latest Products</h2>
            <p class="section-subtitle">Discover our newest arrivals</p>
        </div>
        <div class="products-grid">
            @foreach($latestProducts->take(8) as $product)
                <div class="product-card">
                    <div class="product-image">
                        @if($product->main_image)
                            <img src="{{ $product->main_image }}" alt="{{ $product->name }}" loading="lazy">
                        @else
                            <div class="no-image">📦</div>
                        @endif
                        <div class="product-badges">
                            <span class="badge new">✨ New</span>
                        </div>
                    </div>
                    <div class="product-info">
                        <div class="product-category">{{ $product->category->name }}</div>
                        <h3 class="product-name">{{ $product->name }}</h3>
                        <p class="product-description">{{ Str::limit($product->description, 80) }}</p>
                        <div class="product-meta">
                            <div class="product-price">
                                <span class="price">${{ number_format($product->base_price, 2) }}</span>
                                @if($product->products->count() > 1)
                                    <span class="price-range">
                                        - ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
                                    </span>
                                @endif
                            </div>
                            @if($product->rating)
                                <div class="product-rating">
                                    @for($i = 1; $i <= 5; $i++)
                                        <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                                    @endfor
                                    <span class="rating-text">({{ number_format($product->rating, 1) }})</span>
                                </div>
                            @endif
                        </div>
                        <div class="product-variants">
                            @if($product->products->count() > 0)
                                <span class="variants-count">{{ $product->products->count() }} variants available</span>
                            @endif
                        </div>
                        <div class="product-actions">
                            <a href="{{ url('/products/' . $product->id) }}" class="btn btn-primary btn-sm">View Details</a>
                            <button class="btn btn-outline btn-sm" onclick="addToWishlist({{ $product->id }})">❤️</button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
        <div class="section-footer">
            <a href="{{ url('/products') }}" class="btn btn-outline">View All Products →</a>
        </div>
    </div>
</section>
@endif

<!-- Popular Tags Section -->
@if($popularTags->count() > 0)
<section class="tags-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">🏷️ Popular Tags</h2>
            <p class="section-subtitle">Find products by popular tags</p>
        </div>
        <div class="tags-cloud">
            @foreach($popularTags as $tag)
                <a href="{{ url('/products?tag=' . $tag->id) }}" class="tag-item">
                    {{ $tag->name }}
                    <span class="tag-count">({{ $tag->product_groups_count }})</span>
                </a>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Newsletter Section -->
<section class="newsletter-section">
    <div class="container">
        <div class="newsletter-content">
            <div class="newsletter-text">
                <h2>📧 Stay Updated</h2>
                <p>Subscribe to our newsletter and get the latest updates on new products, special offers, and exclusive deals.</p>
            </div>
            <div class="newsletter-form">
                <form action="#" method="POST" class="subscribe-form">
                    @csrf
                    <input type="email" name="email" placeholder="Enter your email address" class="email-input" required>
                    <button type="submit" class="btn btn-primary">Subscribe</button>
                </form>
            </div>
        </div>
    </div>
</section>

@push('styles')
<style>
    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 4rem;
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .hero-stats {
        display: flex;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .stat {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        color: #fbbf24;
    }

    .stat-label {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .hero-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .hero-image {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .hero-graphic {
        position: relative;
        width: 300px;
        height: 300px;
    }

    .floating-card {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
        animation: float 3s ease-in-out infinite;
    }

    .floating-card:nth-child(1) {
        top: 20px;
        left: 20px;
        animation-delay: 0s;
    }

    .floating-card:nth-child(2) {
        top: 100px;
        right: 20px;
        animation-delay: 1s;
    }

    .floating-card:nth-child(3) {
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        animation-delay: 2s;
    }

    .card-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .card-text {
        font-size: 0.875rem;
        font-weight: 500;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Sections */
    .categories-section, .featured-section, .latest-section, .tags-section {
        margin-bottom: 4rem;
    }

    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1f2937;
    }

    .section-subtitle {
        font-size: 1.125rem;
        color: #6b7280;
    }

    .section-footer {
        text-align: center;
        margin-top: 3rem;
    }

    /* Categories Grid */
    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }

    .category-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        text-decoration: none;
        color: inherit;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .category-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .category-name {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1f2937;
    }

    .category-count {
        color: #6b7280;
        font-size: 0.875rem;
    }

    /* Products Grid */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
    }

    .product-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .product-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .no-image {
        width: 100%;
        height: 100%;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: #d1d5db;
    }

    .product-badges {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .badge.trending {
        background: #fbbf24;
        color: #92400e;
    }

    .badge.new {
        background: #34d399;
        color: #065f46;
    }

    .product-info {
        padding: 1.5rem;
    }

    .product-category {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .product-name {
        font-size: 1.125rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1f2937;
    }

    .product-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .price {
        font-size: 1.25rem;
        font-weight: 700;
        color: #059669;
    }

    .price-range {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .star {
        font-size: 0.875rem;
        color: #d1d5db;
    }

    .star.filled {
        color: #fbbf24;
    }

    .rating-text {
        font-size: 0.75rem;
        color: #6b7280;
        margin-left: 0.25rem;
    }

    .product-variants {
        margin-bottom: 1rem;
    }

    .variants-count {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .product-actions {
        display: flex;
        gap: 0.5rem;
    }

    .product-actions .btn {
        flex: 1;
    }

    /* Tags Cloud */
    .tags-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
    }

    .tag-item {
        background: white;
        color: #374151;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .tag-item:hover {
        background: #3b82f6;
        color: white;
        transform: translateY(-2px);
    }

    .tag-count {
        font-size: 0.875rem;
        opacity: 0.7;
    }

    /* Newsletter Section */
    .newsletter-section {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: white;
        padding: 4rem 0;
        margin-top: 4rem;
    }

    .newsletter-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
    }

    .newsletter-text h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .newsletter-text p {
        font-size: 1.125rem;
        opacity: 0.9;
        line-height: 1.6;
    }

    .subscribe-form {
        display: flex;
        gap: 1rem;
    }

    .email-input {
        flex: 1;
        padding: 0.875rem 1rem;
        border: 2px solid #374151;
        border-radius: 8px;
        font-size: 1rem;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transition: all 0.3s ease;
    }

    .email-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .email-input:focus {
        outline: none;
        border-color: #3b82f6;
        background: rgba(255, 255, 255, 0.15);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .hero-content {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .hero-title {
            font-size: 2rem;
        }

        .hero-stats {
            justify-content: center;
        }

        .newsletter-content {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .subscribe-form {
            flex-direction: column;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }

        .categories-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function addToWishlist(productId) {
        @auth
            // Add to wishlist functionality
            fetch('/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ product_id: productId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Added to wishlist!');
                } else {
                    alert('❌ ' + (data.message || 'Failed to add to wishlist'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Failed to add to wishlist');
            });
        @else
            alert('Please login to add items to your wishlist');
            window.location.href = '/login';
        @endauth
    }
</script>
@endpush
@endsection
