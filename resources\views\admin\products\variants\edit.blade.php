@extends('admin.layouts.app')

@section('title', 'Edit Product Variant')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">✏️ Edit Product Variant</h1>
            <p class="page-subtitle">{{ $productGroup->name }} - {{ $variant->color }} {{ $variant->size }}</p>
        </div>
        <div class="header-actions">
            <a href="{{ route('admin.products.variants.index', $productGroup) }}" class="btn btn-outline">
                ← Back to Variants
            </a>
            <a href="{{ route('admin.products.show', $productGroup) }}" class="btn btn-secondary">
                👁️ View Product
            </a>
        </div>
    </div>
</div>

<!-- Product Info -->
<div class="product-info-card">
    <div class="product-summary">
        <div class="product-image">
            @if($productGroup->main_image)
                <img src="{{ $productGroup->main_image }}" alt="{{ $productGroup->name }}">
            @else
                <div class="no-image">📦</div>
            @endif
        </div>
        <div class="product-details">
            <h3>{{ $productGroup->name }}</h3>
            <p>{{ Str::limit($productGroup->description, 100) }}</p>
            <div class="product-meta">
                <span class="meta-item">Base Price: ${{ number_format($productGroup->base_price, 2) }}</span>
                <span class="meta-item">Category: {{ $productGroup->category->name }}</span>
            </div>
        </div>
    </div>
</div>

<div class="form-card">
    <form action="{{ route('admin.products.variants.update', [$productGroup, $variant]) }}" method="POST" class="variant-form">
        @csrf
        @method('PUT')
        
        <div class="form-section">
            <h3 class="section-title">🎨 Variant Details</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="color" class="form-label">Color *</label>
                    <input type="text" id="color" name="color" class="form-control @error('color') is-invalid @enderror" 
                           value="{{ old('color', $variant->color) }}" required placeholder="e.g., Red, Blue, Black">
                    @error('color')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label for="size" class="form-label">Size *</label>
                    <input type="text" id="size" name="size" class="form-control @error('size') is-invalid @enderror" 
                           value="{{ old('size', $variant->size) }}" required placeholder="e.g., S, M, L, XL">
                    @error('size')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">💰 Pricing & Inventory</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="price_adjustment" class="form-label">Price Adjustment ($) *</label>
                    <input type="number" id="price_adjustment" name="price_adjustment" step="0.01"
                           class="form-control @error('price_adjustment') is-invalid @enderror" 
                           value="{{ old('price_adjustment', $variant->price_adjustment) }}" required placeholder="0.00">
                    @error('price_adjustment')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text">
                        Final Price: ${{ number_format($productGroup->base_price + $variant->price_adjustment, 2) }}
                        (Base: ${{ number_format($productGroup->base_price, 2) }} + Adjustment: ${{ number_format($variant->price_adjustment, 2) }})
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="quantity" class="form-label">Stock Quantity *</label>
                    <input type="number" id="quantity" name="quantity" min="0"
                           class="form-control @error('quantity') is-invalid @enderror" 
                           value="{{ old('quantity', $variant->quantity) }}" required placeholder="0">
                    @error('quantity')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text">Current stock level for this variant.</small>
                </div>
            </div>
            
            <div class="form-group">
                <label for="sku" class="form-label">SKU (Stock Keeping Unit)</label>
                <input type="text" id="sku" name="sku" class="form-control @error('sku') is-invalid @enderror" 
                       value="{{ old('sku', $variant->sku) }}" placeholder="Auto-generated if empty">
                @error('sku')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Leave empty to auto-generate a unique SKU.</small>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">📷 Variant Image</h3>
            
            <div class="form-group">
                <label class="form-label">Variant Image</label>
                
                <!-- Current Image Display -->
                @if($variant->image)
                    <div class="current-image-section">
                        <h4>Current Image:</h4>
                        <div class="current-image-display">
                            <img src="{{ $variant->image }}" alt="{{ $variant->color }} {{ $variant->size }}" class="current-image">
                            <div class="current-image-info">
                                <p><strong>Current URL:</strong> {{ $variant->image }}</p>
                                <button type="button" onclick="removeCurrentImage()" class="btn btn-warning btn-sm">
                                    🗑️ Remove Current Image
                                </button>
                            </div>
                        </div>
                    </div>
                @endif
                
                <!-- Image Upload Area -->
                <div class="image-upload-area" id="imageUploadArea">
                    <div class="upload-placeholder" id="uploadPlaceholder">
                        <div class="upload-icon">📷</div>
                        <p>{{ $variant->image ? 'Click to replace image or drag & drop' : 'Click to upload image or drag & drop' }}</p>
                        <small>Supports: JPEG, PNG, GIF, WebP (Max: 5MB)</small>
                    </div>
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" src="" alt="Preview">
                        <div class="image-actions">
                            <button type="button" onclick="removeNewImage()" class="remove-btn">✕</button>
                        </div>
                    </div>
                </div>
                
                <!-- Hidden file input -->
                <input type="file" id="imageFile" accept="image/*" style="display: none;">
                
                <!-- Hidden URL input (will be populated after upload) -->
                <input type="hidden" id="image" name="image" value="{{ old('image', $variant->image) }}">
                
                <!-- Alternative URL input -->
                <div class="url-input-section">
                    <label class="form-label">Or enter image URL:</label>
                    <input type="url" id="image_url" class="form-control" 
                           placeholder="https://example.com/image.jpg">
                    <button type="button" onclick="loadImageFromUrl()" class="btn btn-outline btn-sm">Load Image</button>
                </div>
                
                @error('image')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Upload a new image file or enter a URL for this specific variant.</small>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">⚙️ Availability</h3>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="is_available" value="1" 
                           {{ old('is_available', $variant->is_available) ? 'checked' : '' }}>
                    <span class="checkmark"></span>
                    Available for Purchase
                </label>
                <small class="form-text">Available variants can be purchased by customers.</small>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                ✅ Update Variant
            </button>
            <a href="{{ route('admin.products.variants.index', $productGroup) }}" class="btn btn-outline">
                Cancel
            </a>
        </div>
    </form>
</div>

<!-- Variant Info -->
<div class="info-card">
    <h3 class="info-title">📊 Variant Information</h3>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">Variant ID:</span>
            <span class="info-value">{{ $variant->id }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Created:</span>
            <span class="info-value">{{ $variant->created_at->format('M d, Y H:i') }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Last Updated:</span>
            <span class="info-value">{{ $variant->updated_at->format('M d, Y H:i') }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Current SKU:</span>
            <span class="info-value">{{ $variant->sku ?: 'Not set' }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Availability:</span>
            <span class="info-value">{{ $variant->is_available ? 'Available' : 'Unavailable' }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Final Price:</span>
            <span class="info-value">${{ number_format($productGroup->base_price + $variant->price_adjustment, 2) }}</span>
        </div>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .product-info-card, .form-card, .info-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .product-info-card {
        padding: 1.5rem;
    }

    .product-summary {
        display: flex;
        gap: 1.5rem;
        align-items: center;
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        font-size: 2rem;
        color: #ccc;
    }

    .product-details h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .product-details p {
        color: #666;
        margin-bottom: 1rem;
    }

    .product-meta {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .meta-item {
        background: #f8f9fa;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        color: #666;
    }

    .variant-form {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e9ecef;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.875rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-control.is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: #666;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        font-weight: 500;
        color: #333;
    }

    .checkbox-label input[type="checkbox"] {
        width: 1.2rem;
        height: 1.2rem;
        margin: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }

    /* Current Image Styles */
    .current-image-section {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
    }

    .current-image-section h4 {
        margin-bottom: 1rem;
        color: #333;
        font-size: 1rem;
    }

    .current-image-display {
        display: flex;
        gap: 1.5rem;
        align-items: center;
    }

    .current-image {
        width: 120px;
        height: 120px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .current-image-info {
        flex: 1;
    }

    .current-image-info p {
        margin-bottom: 1rem;
        word-break: break-all;
        color: #666;
        font-size: 0.9rem;
    }

    /* Image Upload Styles */
    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .image-upload-area:hover {
        border-color: #667eea;
        background: #f8f9fa;
    }

    .image-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .upload-placeholder {
        color: #666;
    }

    .upload-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .image-preview {
        position: relative;
        display: inline-block;
    }

    .image-preview img {
        max-width: 300px;
        max-height: 200px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .image-actions {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .remove-btn {
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        cursor: pointer;
        font-size: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-btn:hover {
        background: #dc3545;
    }

    .url-input-section {
        display: flex;
        gap: 0.5rem;
        align-items: end;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .url-input-section .form-control {
        flex: 1;
    }

    .url-input-section .form-label {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: #666;
    }

    .info-card {
        padding: 2rem;
    }

    .info-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .info-label {
        font-weight: 500;
        color: #666;
    }

    .info-value {
        font-weight: 600;
        color: #333;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
        }

        .product-summary {
            flex-direction: column;
            text-align: center;
        }

        .product-meta {
            justify-content: center;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .current-image-display {
            flex-direction: column;
            text-align: center;
        }

        .url-input-section {
            flex-direction: column;
        }

        .image-preview img {
            max-width: 100%;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('imageUploadArea');
    const fileInput = document.getElementById('imageFile');
    const placeholder = document.getElementById('uploadPlaceholder');
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const hiddenInput = document.getElementById('image');
    
    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            uploadImage(file);
        }
    });
    
    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            uploadImage(files[0]);
        }
    });
    
    // Upload image function
    function uploadImage(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file.');
            return;
        }
        
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }
        
        // Show loading
        placeholder.innerHTML = '<div class="upload-icon">⏳</div><p>Uploading...</p>';
        
        // Create FormData
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'variant');
        
        // Upload via AJAX
        fetch('/admin/images/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show preview
                previewImg.src = data.url;
                hiddenInput.value = data.url;
                placeholder.style.display = 'none';
                preview.style.display = 'block';
            } else {
                alert('Upload failed: ' + (data.error || 'Unknown error'));
                resetUploadArea();
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            alert('Upload failed. Please try again.');
            resetUploadArea();
        });
    }
    
    // Reset upload area
    function resetUploadArea() {
        placeholder.innerHTML = '<div class="upload-icon">📷</div><p>Click to upload image or drag & drop</p><small>Supports: JPEG, PNG, GIF, WebP (Max: 5MB)</small>';
        placeholder.style.display = 'block';
        preview.style.display = 'none';
        fileInput.value = '';
    }
    
    // Remove new image function
    window.removeNewImage = function() {
        resetUploadArea();
        hiddenInput.value = '{{ $variant->image }}'; // Reset to original
    };
    
    // Remove current image function
    window.removeCurrentImage = function() {
        if (confirm('Are you sure you want to remove the current image?')) {
            hiddenInput.value = '';
            document.querySelector('.current-image-section').style.display = 'none';
        }
    };
    
    // Load image from URL
    window.loadImageFromUrl = function() {
        const url = document.getElementById('image_url').value.trim();
        if (url) {
            previewImg.src = url;
            hiddenInput.value = url;
            placeholder.style.display = 'none';
            preview.style.display = 'block';
            document.getElementById('image_url').value = '';
        }
    };
});
</script>
@endpush
@endsection
