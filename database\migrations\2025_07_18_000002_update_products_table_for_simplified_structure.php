<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop existing products table and recreate with new structure
        Schema::dropIfExists('products');
        
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_group_id')->constrained()->onDelete('cascade');
            $table->string('color', 50);
            $table->string('size', 20);
            $table->decimal('price_adjustment', 8, 2)->default(0); // Can be negative or positive
            $table->integer('quantity')->default(0);
            $table->string('sku')->unique();
            $table->string('image')->nullable();
            $table->boolean('is_available')->default(true);
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['product_group_id', 'is_available']);
            $table->index(['color', 'size']);
            $table->index('sku');
            
            // Ensure unique combination of product_group_id, color, and size
            $table->unique(['product_group_id', 'color', 'size']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
        
        // Recreate the old products table structure if needed
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2);
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_trending')->default(false);
            $table->decimal('rating', 3, 2)->default(0);
            $table->timestamps();
        });
    }
};
