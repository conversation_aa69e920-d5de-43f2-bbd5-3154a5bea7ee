<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductGroupController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\TagController;

Route::middleware('api')->group(function () {
    Route::get('/test', function () {
        return response()->json(['message' => 'API routes are working!']);
    });
});

// Routes for Product Groups (main products)
Route::prefix('product-groups')->group(function () {
    Route::get('/', [ProductGroupController::class, 'index']);
    Route::get('/homepage', [ProductGroupController::class, 'homepage']);
    Route::get('/form-data', [ProductGroupController::class, 'getFormData']);
    Route::get('/{productGroup}', [ProductGroupController::class, 'show']);
    Route::post('/', [ProductGroupController::class, 'store']);
    Route::put('/{productGroup}', [ProductGroupController::class, 'update']);
    Route::delete('/{productGroup}', [ProductGroupController::class, 'destroy']);
});

// Routes for Categories
Route::prefix('categories')->group(function () {
    Route::get('/', [CategoryController::class, 'index']);
    Route::post('/', [CategoryController::class, 'store']);
    Route::get('/{category}', [CategoryController::class, 'show']);
    Route::put('/{category}', [CategoryController::class, 'update']);
    Route::delete('/{category}', [CategoryController::class, 'destroy']);
});

// Routes for Tags
Route::prefix('tags')->group(function () {
    Route::get('/', [TagController::class, 'index']);
    Route::post('/', [TagController::class, 'store']);
    Route::get('/{tag}', [TagController::class, 'show']);
    Route::put('/{tag}', [TagController::class, 'update']);
    Route::delete('/{tag}', [TagController::class, 'destroy']);
});

// Backward compatibility routes (optional - can be removed later)
Route::prefix('products')->group(function () {
    Route::get('/', [ProductGroupController::class, 'index']);
    Route::get('/homepage', [ProductGroupController::class, 'homepage']);
    Route::get('/{productGroup}', [ProductGroupController::class, 'show']);
});
