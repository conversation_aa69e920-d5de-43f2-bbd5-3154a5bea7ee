# Simplified Product Management System Documentation

## Overview

This documentation covers the new simplified product management system that replaces the complex multi-table structure with a streamlined approach using Product Groups and Product Variants.

## Database Structure

### Core Tables

#### 1. `product_groups`
The main product entity containing base information.

```sql
- id (Primary Key)
- name (Product name - e.g., "Cotton T-Shirt")
- description (Product description)
- category_id (Foreign Key to categories)
- base_price (Base price in USD)
- is_active (Boolean - product visibility)
- is_trending (Boolean - featured products)
- rating (Decimal 0-5)
- main_image (Main product image URL)
- created_at, updated_at
```

#### 2. `products` (Product Variants)
Individual color/size combinations for each product group.

```sql
- id (Primary Key)
- product_group_id (Foreign Key to product_groups)
- color (String - e.g., "Red", "Blue")
- size (String - e.g., "S", "M", "L")
- price_adjustment (Decimal - price difference from base_price)
- quantity (Integer - stock quantity)
- sku (String - unique product code)
- image (String - variant-specific image URL)
- is_available (Boolean - variant availability)
- created_at, updated_at
```

#### 3. `categories`
Product categories for organization.

```sql
- id (Primary Key)
- name (Category name)
- description (Category description)
- created_at, updated_at
```

#### 4. `tags`
Tags for product discovery and similar product recommendations.

```sql
- id (Primary Key)
- name (Tag name)
- created_at, updated_at
```

#### 5. `product_group_tags` (Pivot Table)
Many-to-many relationship between product groups and tags.

```sql
- id (Primary Key)
- product_group_id (Foreign Key)
- tag_id (Foreign Key)
- created_at, updated_at
```

## API Endpoints

### Product Groups

#### Get All Product Groups
```
GET /api/product-groups
```

**Query Parameters:**
- `per_page` - Items per page (default: 12, max: 50)
- `category_id` - Filter by category
- `trending` - Filter trending products (true/false)
- `search` - Search in product names
- `tags` - Filter by tag IDs (comma-separated)
- `min_price`, `max_price` - Price range filter
- `in_stock` - Only show products with available stock
- `sort_by` - Sort field (name, base_price, rating, created_at)
- `sort_order` - Sort direction (asc, desc)

#### Get Homepage Products (Optimized)
```
GET /api/product-groups/homepage
```

**Query Parameters:**
- `per_page` - Items per page (default: 12, max: 24)

#### Get Single Product Group
```
GET /api/product-groups/{id}
```

**Response includes:**
- Product group details
- All available variants
- Available colors and sizes
- Price range
- Total stock
- Similar products (based on shared tags)

#### Create Product Group
```
POST /api/product-groups
```

**Request Body:**
```json
{
  "name": "Cotton T-Shirt",
  "description": "High quality cotton t-shirt",
  "category_id": 1,
  "base_price": 29.99,
  "is_active": true,
  "is_trending": false,
  "rating": 4.5,
  "main_image": "path/to/image.jpg",
  "tags": [1, 2, 3],
  "variants": [
    {
      "color": "Red",
      "size": "S",
      "price_adjustment": 0,
      "quantity": 10,
      "image": "path/to/red-s.jpg"
    },
    {
      "color": "Red",
      "size": "M",
      "price_adjustment": 0,
      "quantity": 15,
      "image": "path/to/red-m.jpg"
    }
  ]
}
```

#### Update Product Group
```
PUT /api/product-groups/{id}
```

#### Delete Product Group
```
DELETE /api/product-groups/{id}
```

### Product Variants

#### Get All Variants
```
GET /api/product-variants
```

**Query Parameters:**
- `product_group_id` - Filter by product group
- `color` - Filter by color
- `size` - Filter by size
- `available_only` - Only available variants

#### Create Variant
```
POST /api/product-variants
```

#### Update Variant
```
PUT /api/product-variants/{id}
```

#### Delete Variant
```
DELETE /api/product-variants/{id}
```

#### Update Stock
```
PATCH /api/product-variants/{id}/stock
```

#### Get Variants by Color/Size
```
GET /api/product-variants/group/{productGroupId}/color/{color}
GET /api/product-variants/group/{productGroupId}/size/{size}
```

### Categories

#### Standard CRUD Operations
```
GET /api/categories
POST /api/categories
GET /api/categories/{id}
PUT /api/categories/{id}
DELETE /api/categories/{id}
```

### Tags

#### Standard CRUD Operations
```
GET /api/tags
POST /api/tags
GET /api/tags/{id}
PUT /api/tags/{id}
DELETE /api/tags/{id}
```

## Model Relationships

### ProductGroup Model
```php
// Relationships
public function category() // belongsTo
public function products() // hasMany (variants)
public function availableProducts() // hasMany with conditions
public function tags() // belongsToMany

// Attributes
public function getAvailableColorsAttribute() // unique colors
public function getAvailableSizesAttribute() // unique sizes
public function getPriceRangeAttribute() // min/max prices
public function getTotalStockAttribute() // total quantity

// Scopes
public function scopeActive($query)
public function scopeTrending($query)
public function scopeInStock($query)

// Methods
public function getSimilarProducts($limit = 6)
```

### Product Model (Variant)
```php
// Relationships
public function productGroup() // belongsTo

// Attributes
public function getFinalPriceAttribute() // base_price + price_adjustment
public function getFullNameAttribute() // name + color + size
public function getInStockAttribute() // is_available && quantity > 0

// Scopes
public function scopeAvailable($query)
public function scopeColor($query, $color)
public function scopeSize($query, $size)

// Static Methods
public static function generateSku($productGroupId, $color, $size)
```

## Usage Examples

### Frontend Product Display
```javascript
// Get products for homepage
const response = await fetch('/api/product-groups/homepage?per_page=12');
const data = await response.json();

// Display products
data.data.forEach(product => {
  console.log(`${product.name} - $${product.base_price}`);
});
```

### Product Detail Page
```javascript
// Get product with all variants
const response = await fetch('/api/product-groups/1');
const data = await response.json();

const productGroup = data.product_group;
const availableColors = data.available_colors;
const availableSizes = data.available_sizes;
const priceRange = data.price_range;
const similarProducts = data.similar_products;
```

### Search and Filter
```javascript
// Search products
const response = await fetch('/api/product-groups?search=shirt&category_id=1&tags=1,2&min_price=20&max_price=50');
```

### Create Product with Variants
```javascript
const productData = {
  name: "Summer Dress",
  description: "Light summer dress",
  category_id: 2,
  base_price: 49.99,
  tags: [1, 3, 5],
  variants: [
    { color: "Blue", size: "S", price_adjustment: 0, quantity: 5 },
    { color: "Blue", size: "M", price_adjustment: 0, quantity: 8 },
    { color: "Red", size: "S", price_adjustment: 5, quantity: 3 },
    { color: "Red", size: "M", price_adjustment: 5, quantity: 6 }
  ]
};

const response = await fetch('/api/product-groups', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(productData)
});
```

## Admin Interface

The system includes a comprehensive admin panel (`admin_panel.html`) with:

### Dashboard
- Statistics overview
- Quick access to all sections

### Product Groups Management
- View all product groups
- Create new product groups with variants
- Edit and delete product groups

### Variants Management
- View all product variants
- Manage stock levels
- Edit variant details

### Categories Management
- Create, edit, delete categories
- View category hierarchy

### Tags Management
- Create, edit, delete tags
- Manage tag associations

## Benefits of This Structure

### 1. Simplicity
- Only 5 tables instead of 8-10
- Direct color/size storage as strings
- Easier to understand and maintain

### 2. Flexibility
- Different prices for different variants
- Individual stock management per variant
- Separate images per variant

### 3. Performance
- Fewer JOINs required
- Better indexing opportunities
- Optimized queries for common operations

### 4. Scalability
- Easy to add new attributes
- Simple to extend functionality
- Clear separation of concerns

## Migration from Old Structure

If migrating from the complex structure:

1. Run the new migrations
2. Create data migration scripts to:
   - Convert existing products to product groups
   - Convert product variants to the new products table
   - Migrate tags relationships
3. Update frontend code to use new API endpoints
4. Test thoroughly before going live

## Testing

Use the provided test files:
- `test_new_structure.html` - API testing interface
- `admin_panel.html` - Full admin interface

Both files include comprehensive testing capabilities for all API endpoints and functionality.
