<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Wishlist;
use App\Models\User;
use App\Models\ProductGroup;

class WishlistSeeder extends Seeder
{
    public function run(): void
    {
        // Check if we have users and product groups
        $user = User::first();
        if (!$user) {
            $this->command->warn('No users found. Please run UsersSeeder first.');
            return;
        }

        $productGroups = ProductGroup::inRandomOrder()->take(3)->get();
        if ($productGroups->isEmpty()) {
            $this->command->warn('No product groups found. Please run ProductGroupSeeder first.');
            return;
        }

        foreach ($productGroups as $productGroup) {
            Wishlist::create([
                'user_id' => $user->id,
                'product_id' => $productGroup->id, // This should reference ProductGroup
            ]);
        }

        $this->command->info('Wishlist items created successfully!');
    }
}
