<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .controls input, .controls select, .controls button {
            margin: 5px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls button {
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .product-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .product-price {
            font-size: 16px;
            color: #28a745;
            font-weight: bold;
        }
        .product-rating {
            color: #ffc107;
        }
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        .pagination button {
            margin: 0 5px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .pagination button:hover {
            background: #f8f9fa;
        }
        .pagination button.active {
            background: #007bff;
            color: white;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            color: #dc3545;
            padding: 10px;
            background: #f8d7da;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API المنتجات مع Pagination</h1>
        
        <div class="controls">
            <input type="text" id="search" placeholder="البحث في المنتجات...">
            <select id="perPage">
                <option value="6">6 منتجات</option>
                <option value="12" selected>12 منتج</option>
                <option value="24">24 منتج</option>
            </select>
            <select id="sortBy">
                <option value="created_at">تاريخ الإنشاء</option>
                <option value="name">الاسم</option>
                <option value="price">السعر</option>
                <option value="rating">التقييم</option>
            </select>
            <select id="sortOrder">
                <option value="desc">تنازلي</option>
                <option value="asc">تصاعدي</option>
            </select>
            <button onclick="loadProducts(1)">تحميل المنتجات</button>
            <button onclick="loadHomepageProducts(1)">منتجات الصفحة الرئيسية</button>
        </div>

        <div id="loading" class="loading" style="display: none;">جاري التحميل...</div>
        <div id="error" class="error" style="display: none;"></div>
        
        <div id="products" class="product-grid"></div>
        
        <div id="pagination" class="pagination"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        let currentEndpoint = 'products';

        async function loadProducts(page = 1) {
            currentEndpoint = 'products';
            await fetchProducts(`${API_BASE}/products`, page);
        }

        async function loadHomepageProducts(page = 1) {
            currentEndpoint = 'products/homepage';
            await fetchProducts(`${API_BASE}/products/homepage`, page);
        }

        async function fetchProducts(url, page = 1) {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const productsContainer = document.getElementById('products');
            const paginationContainer = document.getElementById('pagination');

            loading.style.display = 'block';
            error.style.display = 'none';
            productsContainer.innerHTML = '';
            paginationContainer.innerHTML = '';

            try {
                const params = new URLSearchParams({
                    page: page,
                    per_page: document.getElementById('perPage').value,
                    search: document.getElementById('search').value,
                    sort_by: document.getElementById('sortBy').value,
                    sort_order: document.getElementById('sortOrder').value,
                });

                const response = await fetch(`${url}?${params}`);
                const data = await response.json();

                loading.style.display = 'none';

                if (!response.ok) {
                    throw new Error(data.message || 'حدث خطأ في تحميل البيانات');
                }

                displayProducts(data);
                displayPagination(data);

            } catch (err) {
                loading.style.display = 'none';
                error.style.display = 'block';
                error.textContent = err.message;
            }
        }

        function displayProducts(data) {
            const container = document.getElementById('products');
            const products = data.data || data;

            if (!products || products.length === 0) {
                container.innerHTML = '<p>لا توجد منتجات للعرض</p>';
                return;
            }

            container.innerHTML = products.map(product => `
                <div class="product-card">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price} ريال</div>
                    <div class="product-rating">⭐ ${product.rating || 'غير مقيم'}</div>
                    ${product.is_trending ? '<div style="color: #dc3545;">🔥 رائج</div>' : ''}
                    ${product.description ? `<p>${product.description}</p>` : ''}
                </div>
            `).join('');
        }

        function displayPagination(data) {
            const container = document.getElementById('pagination');
            
            if (data.pagination) {
                // للصفحة الرئيسية
                const { current_page, last_page, has_more_pages } = data.pagination;
                
                let paginationHTML = '';
                
                if (current_page > 1) {
                    paginationHTML += `<button onclick="fetchProducts('${API_BASE}/${currentEndpoint}', ${current_page - 1})">السابق</button>`;
                }
                
                paginationHTML += `<span>الصفحة ${current_page} من ${last_page}</span>`;
                
                if (has_more_pages) {
                    paginationHTML += `<button onclick="fetchProducts('${API_BASE}/${currentEndpoint}', ${current_page + 1})">التالي</button>`;
                }
                
                container.innerHTML = paginationHTML;
            } else if (data.links) {
                // للمنتجات العادية
                const { current_page, last_page } = data;
                
                let paginationHTML = '';
                
                if (current_page > 1) {
                    paginationHTML += `<button onclick="fetchProducts('${API_BASE}/${currentEndpoint}', ${current_page - 1})">السابق</button>`;
                }
                
                for (let i = Math.max(1, current_page - 2); i <= Math.min(last_page, current_page + 2); i++) {
                    const activeClass = i === current_page ? 'active' : '';
                    paginationHTML += `<button class="${activeClass}" onclick="fetchProducts('${API_BASE}/${currentEndpoint}', ${i})">${i}</button>`;
                }
                
                if (current_page < last_page) {
                    paginationHTML += `<button onclick="fetchProducts('${API_BASE}/${currentEndpoint}', ${current_page + 1})">التالي</button>`;
                }
                
                container.innerHTML = paginationHTML;
            }
        }

        // تحميل المنتجات عند فتح الصفحة
        window.onload = () => loadHomepageProducts(1);
    </script>
</body>
</html>
