<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductGroup;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    /**
     * Get all products with filtering and pagination.
     */
    public function index(Request $request): JsonResponse
    {
        $query = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('tags', function($tagQuery) use ($search) {
                      $tagQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Tag filter
        if ($request->filled('tag')) {
            $query->whereHas('tags', function($tagQuery) use ($request) {
                $tagQuery->where('tags.id', $request->tag);
            });
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('base_price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('base_price', '<=', $request->max_price);
        }

        // Rating filter
        if ($request->filled('rating')) {
            $query->where('rating', '>=', $request->rating);
        }

        // Trending filter
        if ($request->filled('trending') && $request->trending === 'true') {
            $query->where('is_trending', true);
        }

        // Sorting
        $sort = $request->get('sort', 'latest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('base_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('base_price', 'desc');
                break;
            case 'rating':
                $query->orderBy('rating', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'trending':
                $query->orderBy('is_trending', 'desc')->orderBy('created_at', 'desc');
                break;
            default:
                $query->latest();
        }

        $perPage = $request->get('per_page', 12);
        $products = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'from' => $products->firstItem(),
                'to' => $products->lastItem(),
            ],
            'filters' => [
                'categories' => Category::where('is_active', true)->get(['id', 'name']),
                'tags' => Tag::withCount('productGroups')->having('product_groups_count', '>', 0)->get(['id', 'name']),
                'price_range' => [
                    'min' => ProductGroup::where('is_active', true)->min('base_price'),
                    'max' => ProductGroup::where('is_active', true)->max('base_price'),
                ]
            ]
        ]);
    }

    /**
     * Get a specific product with variants.
     */
    public function show(ProductGroup $product): JsonResponse
    {
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found or inactive'
            ], 404);
        }

        $product->load(['category', 'tags', 'products' => function($query) {
            $query->where('is_available', true)->orderBy('color')->orderBy('size');
        }]);

        // Get related products
        $relatedProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->take(4)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $product,
            'related_products' => $relatedProducts
        ]);
    }

    /**
     * Get featured/trending products.
     */
    public function featured(): JsonResponse
    {
        $products = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('is_trending', true)
            ->latest()
            ->take(8)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Get latest products.
     */
    public function latest(): JsonResponse
    {
        $products = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->latest()
            ->take(12)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Search products.
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([
                'success' => true,
                'data' => []
            ]);
        }

        $products = ProductGroup::with(['category'])
            ->where('is_active', true)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->take(10)
            ->get()
            ->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name,
                    'price' => number_format($product->base_price, 2),
                    'image' => $product->main_image,
                    'description' => $product->description,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * Get product variants for a specific product.
     */
    public function variants(ProductGroup $product): JsonResponse
    {
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found or inactive'
            ], 404);
        }

        $variants = $product->products()
            ->where('is_available', true)
            ->orderBy('color')
            ->orderBy('size')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $variants
        ]);
    }

    /**
     * Get categories with product counts.
     */
    public function categories(): JsonResponse
    {
        $categories = Category::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->where('is_active', true)
        ->orderBy('product_groups_count', 'desc')
        ->get();

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Get tags with product counts.
     */
    public function tags(): JsonResponse
    {
        $tags = Tag::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->having('product_groups_count', '>', 0)
        ->orderBy('product_groups_count', 'desc')
        ->get();

        return response()->json([
            'success' => true,
            'data' => $tags
        ]);
    }

    /**
     * Get homepage data (stats, featured products, categories, etc.).
     */
    public function homepage(): JsonResponse
    {
        // Get featured/trending products
        $featuredProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('is_trending', true)
            ->latest()
            ->take(8)
            ->get();

        // Get latest products
        $latestProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->latest()
            ->take(12)
            ->get();

        // Get categories with product counts
        $categories = Category::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->where('is_active', true)
        ->orderBy('product_groups_count', 'desc')
        ->take(8)
        ->get();

        // Get popular tags
        $popularTags = Tag::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->orderBy('product_groups_count', 'desc')
        ->take(10)
        ->get();

        // Get statistics
        $stats = [
            'total_products' => ProductGroup::where('is_active', true)->count(),
            'total_categories' => Category::where('is_active', true)->count(),
            'total_variants' => ProductGroup::where('is_active', true)
                ->withCount('products')
                ->get()
                ->sum('products_count'),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'featured_products' => $featuredProducts,
                'latest_products' => $latestProducts,
                'categories' => $categories,
                'popular_tags' => $popularTags,
            ]
        ]);
    }
}
