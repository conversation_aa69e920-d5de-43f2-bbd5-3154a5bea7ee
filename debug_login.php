<!DOCTYPE html>
<html>
<head>
    <title>Debug Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { padding: 10px; width: 300px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>🔍 Debug Login Test</h1>
    
    <div class="info">
        <h3>Test Accounts:</h3>
        <ul>
            <li><strong><EMAIL></strong> / password</li>
            <li><strong><EMAIL></strong> / password123</li>
            <li><strong><EMAIL></strong> / password123</li>
            <li><strong><EMAIL></strong> / password123</li>
            <li><strong><EMAIL></strong> / password123</li>
        </ul>
    </div>

    <form method="POST" action="/debug-login">
        <input type="hidden" name="_token" value="<?php echo csrf_token(); ?>">
        
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" name="email" id="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" name="password" id="password" value="password" required>
        </div>
        
        <button type="submit">Test Login</button>
    </form>

    <?php
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Bootstrap Laravel
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
        
        use App\Models\User;
        use Illuminate\Support\Facades\Hash;
        use Illuminate\Support\Facades\Auth;
        
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        
        echo '<div class="result">';
        echo '<h3>🧪 Debug Results:</h3>';
        
        // Check if user exists
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            echo '<div class="error">❌ User not found with email: ' . htmlspecialchars($email) . '</div>';
        } else {
            echo '<div class="success">✅ User found: ' . htmlspecialchars($user->name) . '</div>';
            
            // Check password
            if (Hash::check($password, $user->password)) {
                echo '<div class="success">✅ Password is correct</div>';
                
                // Test Auth::attempt
                $credentials = ['email' => $email, 'password' => $password];
                if (Auth::attempt($credentials)) {
                    echo '<div class="success">✅ Auth::attempt() successful</div>';
                    echo '<div class="info">User authenticated: ' . Auth::user()->name . '</div>';
                    Auth::logout();
                } else {
                    echo '<div class="error">❌ Auth::attempt() failed</div>';
                }
            } else {
                echo '<div class="error">❌ Password is incorrect</div>';
                echo '<div class="info">Expected password hash: ' . substr($user->password, 0, 30) . '...</div>';
            }
        }
        
        echo '</div>';
    }
    ?>
</body>
</html>
