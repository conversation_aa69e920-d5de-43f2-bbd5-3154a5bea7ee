<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductGroup;
use App\Models\Product;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display a listing of product groups.
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to product management.');
        }

        $query = ProductGroup::with(['category', 'tags', 'products']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Trending filter
        if ($request->filled('trending')) {
            $query->where('is_trending', $request->trending === 'yes');
        }

        $productGroups = $query->latest()->paginate(15);
        $categories = Category::where('is_active', true)->get();

        return view('admin.products.index', compact('productGroups', 'categories', 'admin'));
    }

    /**
     * Show the form for creating a new product group.
     */
    public function create()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to create products.');
        }

        $categories = Category::where('is_active', true)->get();
        $tags = Tag::all();

        return view('admin.products.create', compact('categories', 'tags', 'admin'));
    }

    /**
     * Store a newly created product group in storage.
     */
    public function store(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to create products.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'base_price' => 'required|numeric|min:0',
            'main_image' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'is_trending' => 'boolean',
            'rating' => 'nullable|numeric|min:0|max:5',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id',
        ]);

        DB::transaction(function () use ($request) {
            $productGroup = ProductGroup::create([
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'base_price' => $request->base_price,
                'main_image' => $request->main_image,
                'is_active' => $request->boolean('is_active', true),
                'is_trending' => $request->boolean('is_trending', false),
                'rating' => $request->rating ?? 0,
            ]);

            // Attach tags
            if ($request->filled('tags')) {
                $productGroup->tags()->attach($request->tags);
            }
        });

        return redirect()->route('admin.products.index')
            ->with('success', 'Product group created successfully!');
    }

    /**
     * Display the specified product group.
     */
    public function show(ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to view products.');
        }

        $product->load(['category', 'tags', 'products']);

        return view('admin.products.show', compact('product', 'admin'));
    }

    /**
     * Show the form for editing the specified product group.
     */
    public function edit(ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to edit products.');
        }

        $categories = Category::where('is_active', true)->get();
        $tags = Tag::all();
        $product->load(['tags']);

        return view('admin.products.edit', compact('product', 'categories', 'tags', 'admin'));
    }

    /**
     * Update the specified product group in storage.
     */
    public function update(Request $request, ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to update products.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'base_price' => 'required|numeric|min:0',
            'main_image' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'is_trending' => 'boolean',
            'rating' => 'nullable|numeric|min:0|max:5',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id',
        ]);

        DB::transaction(function () use ($request, $product) {
            $product->update([
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'base_price' => $request->base_price,
                'main_image' => $request->main_image,
                'is_active' => $request->boolean('is_active', true),
                'is_trending' => $request->boolean('is_trending', false),
                'rating' => $request->rating ?? 0,
            ]);

            // Sync tags
            if ($request->has('tags')) {
                $product->tags()->sync($request->tags);
            } else {
                $product->tags()->detach();
            }
        });

        return redirect()->route('admin.products.index')
            ->with('success', 'Product group updated successfully!');
    }

    /**
     * Remove the specified product group from storage.
     */
    public function destroy(ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            abort(403, 'Unauthorized access to delete products.');
        }

        $productName = $product->name;
        
        DB::transaction(function () use ($product) {
            // Delete all variants first
            $product->products()->delete();
            
            // Detach tags
            $product->tags()->detach();
            
            // Delete the product group
            $product->delete();
        });

        return redirect()->route('admin.products.index')
            ->with('success', "Product group '{$productName}' deleted successfully!");
    }

    /**
     * Toggle product group status.
     */
    public function toggleStatus(ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to modify products.');
        }

        $product->update(['is_active' => !$product->is_active]);

        $status = $product->is_active ? 'activated' : 'deactivated';
        
        return redirect()->back()
            ->with('success', "Product '{$product->name}' has been {$status}!");
    }

    /**
     * Toggle trending status.
     */
    public function toggleTrending(ProductGroup $product)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to modify products.');
        }

        $product->update(['is_trending' => !$product->is_trending]);

        $status = $product->is_trending ? 'marked as trending' : 'removed from trending';
        
        return redirect()->back()
            ->with('success', "Product '{$product->name}' has been {$status}!");
    }
}
