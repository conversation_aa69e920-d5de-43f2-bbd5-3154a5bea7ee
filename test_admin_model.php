<?php

require_once 'vendor/autoload.php';

use App\Models\Admin;

// Test if the Admin model can be instantiated without errors
try {
    echo "Testing Admin model...\n";
    
    // Create a test admin instance (not saved to database)
    $admin = new Admin([
        'name' => 'Test Admin',
        'email' => '<EMAIL>',
        'role' => 'admin',
        'permissions' => ['manage_products', 'view_analytics'],
        'is_active' => true,
        'can_login' => true,
    ]);
    
    echo "✅ Admin model instantiated successfully\n";
    
    // Test permission methods
    echo "Testing permission methods...\n";
    
    // Test hasPermission method
    $hasPermission = $admin->hasPermission('manage_products');
    echo "✅ hasPermission() method works: " . ($hasPermission ? 'true' : 'false') . "\n";
    
    // Test canPerform method
    $canPerform = $admin->canPerform('manage_products');
    echo "✅ canPerform() method works: " . ($canPerform ? 'true' : 'false') . "\n";
    
    // Test role display
    $roleDisplay = $admin->getRoleDisplayAttribute();
    echo "✅ getRoleDisplayAttribute() works: $roleDisplay\n";
    
    echo "\n🎉 All Admin model tests passed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
