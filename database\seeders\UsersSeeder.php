<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    public function run(): void
    {
        $users = [
            [
                'name' => '<PERSON>',
                'first_name' => '<PERSON>',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+201234567890',
                'date_of_birth' => '1990-05-15',
                'gender' => 'male',
                'address' => '123 Tahrir Square, Downtown',
                'city' => 'Cairo',
                'state' => 'Cairo Governorate',
                'country' => 'Egypt',
                'postal_code' => '11511',
                'bio' => 'Software developer and tech enthusiast from Cairo.',
                'preferred_language' => 'ar',
                'timezone' => 'Africa/Cairo',
                'customer_type' => 'vip',
                'total_spent' => 1250.50,
                'total_orders' => 15,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'first_name' => '<PERSON>ima',
                'last_name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+201234567891',
                'date_of_birth' => '1995-08-22',
                'gender' => 'female',
                'address' => '456 Nile Corniche, Zamalek',
                'city' => 'Cairo',
                'state' => 'Cairo Governorate',
                'country' => 'Egypt',
                'postal_code' => '11211',
                'bio' => 'Fashion designer and entrepreneur.',
                'preferred_language' => 'en',
                'timezone' => 'Africa/Cairo',
                'customer_type' => 'regular',
                'total_spent' => 750.25,
                'total_orders' => 8,
                'facebook_url' => 'https://facebook.com/fatima.hassan',
                'instagram_url' => 'https://instagram.com/fatima_designs',
            ],
            [
                'name' => 'Omar Ali',
                'first_name' => 'Omar',
                'last_name' => 'Ali',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+201234567892',
                'date_of_birth' => '1988-12-10',
                'gender' => 'male',
                'address' => '789 Alexandria Road, Giza',
                'city' => 'Giza',
                'state' => 'Giza Governorate',
                'country' => 'Egypt',
                'postal_code' => '12611',
                'bio' => 'Business owner and sports enthusiast.',
                'preferred_language' => 'en',
                'timezone' => 'Africa/Cairo',
                'customer_type' => 'wholesale',
                'total_spent' => 3200.75,
                'total_orders' => 25,
                'linkedin_url' => 'https://linkedin.com/in/omar-ali',
            ],
            [
                'name' => 'Nour Mahmoud',
                'first_name' => 'Nour',
                'last_name' => 'Mahmoud',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '+201234567893',
                'date_of_birth' => '2000-03-18',
                'gender' => 'female',
                'address' => '321 University Street, New Cairo',
                'city' => 'New Cairo',
                'state' => 'Cairo Governorate',
                'country' => 'Egypt',
                'postal_code' => '11835',
                'bio' => 'University student studying computer science.',
                'preferred_language' => 'ar',
                'timezone' => 'Africa/Cairo',
                'customer_type' => 'regular',
                'total_spent' => 150.00,
                'total_orders' => 3,
                'sms_notifications' => true,
            ],
            [
                'name' => 'Test User',
                'first_name' => 'Test',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'phone' => '+************',
                'preferred_language' => 'en',
                'timezone' => 'Africa/Cairo',
                'customer_type' => 'regular',
            ],
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }

        $this->command->info('Users created successfully!');
        $this->command->info('Test accounts:');
        $this->command->info('- <EMAIL> / password123 (VIP Customer)');
        $this->command->info('- <EMAIL> / password123 (Regular Customer)');
        $this->command->info('- <EMAIL> / password123 (Wholesale Customer)');
        $this->command->info('- <EMAIL> / password123 (Student)');
        $this->command->info('- <EMAIL> / password (Basic Test User)');
    }
}
