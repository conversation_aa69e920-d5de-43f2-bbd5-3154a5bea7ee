<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name', 'Laravel') }} - Welcome</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .welcome-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }

        .logo {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .title {
            font-size: 3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border: 2px solid white;
            border-radius: 50px;
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: transparent;
        }

        .btn:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            background: transparent;
            color: white;
        }

        .features {
            margin-top: 3rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-description {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }

            .buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">🛍️</div>
        <h1 class="title">{{ config('app.name', 'E-Store') }}</h1>
        <p class="subtitle">Modern Product Management System with Simplified Architecture</p>
        
        <div class="buttons">
            @auth
                <a href="{{ route('home') }}" class="btn btn-primary">Go to Store</a>
            @else
                <a href="{{ route('login') }}" class="btn">Login</a>
                <a href="{{ route('register') }}" class="btn btn-primary">Get Started</a>
            @endauth
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">📦</div>
                <div class="feature-title">Product Management</div>
                <div class="feature-description">Simplified product groups with color/size variants</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">Modern Design</div>
                <div class="feature-description">Beautiful, responsive interface built with Blade</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🚀</div>
                <div class="feature-title">High Performance</div>
                <div class="feature-description">Optimized database structure for speed</div>
            </div>
        </div>
    </div>
</body>
</html>
