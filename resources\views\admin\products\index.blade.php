@extends('admin.layouts.app')

@section('title', 'Product Management')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">📦 Product Management</h1>
            <p class="page-subtitle">Manage your product catalog and inventory</p>
        </div>
        @if($admin->hasPermission('manage_products'))
            <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
                ➕ Add New Product
            </a>
        @endif
    </div>
</div>

<!-- Filters -->
<div class="filters-card">
    <form method="GET" action="{{ route('admin.products.index') }}" class="filters-form">
        <div class="filter-group">
            <input type="text" name="search" placeholder="Search products..." 
                   value="{{ request('search') }}" class="filter-input">
        </div>
        
        <div class="filter-group">
            <select name="category" class="filter-select">
                <option value="">All Categories</option>
                @foreach($categories as $category)
                    <option value="{{ $category->id }}" 
                            {{ request('category') == $category->id ? 'selected' : '' }}>
                        {{ $category->name }}
                    </option>
                @endforeach
            </select>
        </div>
        
        <div class="filter-group">
            <select name="status" class="filter-select">
                <option value="">All Status</option>
                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>
        
        <div class="filter-group">
            <select name="trending" class="filter-select">
                <option value="">All Products</option>
                <option value="yes" {{ request('trending') == 'yes' ? 'selected' : '' }}>Trending</option>
                <option value="no" {{ request('trending') == 'no' ? 'selected' : '' }}>Not Trending</option>
            </select>
        </div>
        
        <button type="submit" class="btn btn-secondary">🔍 Filter</button>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline">Clear</a>
    </form>
</div>

<!-- Products Table -->
<div class="table-card">
    <div class="table-header">
        <h3>Products ({{ $productGroups->total() }})</h3>
        <div class="table-actions">
            @if($admin->hasPermission('export_data'))
                <button class="btn btn-outline btn-sm">📊 Export</button>
            @endif
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Category</th>
                    <th>Price</th>
                    <th>Variants</th>
                    <th>Status</th>
                    <th>Rating</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($productGroups as $product)
                    <tr>
                        <td>
                            <div class="product-info">
                                <div class="product-image">
                                    @if($product->main_image)
                                        <img src="{{ $product->main_image }}" alt="{{ $product->name }}">
                                    @else
                                        <div class="no-image">📦</div>
                                    @endif
                                </div>
                                <div class="product-details">
                                    <h4>{{ $product->name }}</h4>
                                    <p>{{ Str::limit($product->description, 60) }}</p>
                                    <div class="product-tags">
                                        @foreach($product->tags->take(3) as $tag)
                                            <span class="tag">{{ $tag->name }}</span>
                                        @endforeach
                                        @if($product->tags->count() > 3)
                                            <span class="tag-more">+{{ $product->tags->count() - 3 }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="category-badge">{{ $product->category->name }}</span>
                        </td>
                        <td>
                            <div class="price-info">
                                <span class="base-price">${{ number_format($product->base_price, 2) }}</span>
                                @if($product->products->count() > 0)
                                    <small class="price-range">
                                        ${{ number_format($product->products->min('price_adjustment') + $product->base_price, 2) }} - 
                                        ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
                                    </small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div class="variants-info">
                                <span class="variant-count">{{ $product->products->count() }} variants</span>
                                <small class="stock-info">
                                    {{ $product->products->sum('quantity') }} in stock
                                </small>
                            </div>
                        </td>
                        <td>
                            <div class="status-badges">
                                <span class="status-badge {{ $product->is_active ? 'active' : 'inactive' }}">
                                    {{ $product->is_active ? 'Active' : 'Inactive' }}
                                </span>
                                @if($product->is_trending)
                                    <span class="status-badge trending">🔥 Trending</span>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div class="rating">
                                @for($i = 1; $i <= 5; $i++)
                                    <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                                @endfor
                                <small>({{ number_format($product->rating, 1) }})</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ route('admin.products.show', $product) }}" 
                                   class="btn btn-sm btn-outline" title="View">👁️</a>
                                
                                <a href="{{ route('admin.products.variants.index', $product) }}" 
                                   class="btn btn-sm btn-secondary" title="Manage Variants">🎨</a>
                                
                                @if($admin->hasPermission('manage_products'))
                                    <a href="{{ route('admin.products.edit', $product) }}" 
                                       class="btn btn-sm btn-primary" title="Edit">✏️</a>
                                    
                                    <form action="{{ route('admin.products.toggle-status', $product) }}" 
                                          method="POST" style="display: inline;">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-sm btn-warning" 
                                                title="{{ $product->is_active ? 'Deactivate' : 'Activate' }}">
                                            {{ $product->is_active ? '⏸️' : '▶️' }}
                                        </button>
                                    </form>
                                @endif
                                
                                @if($admin->hasPermission('delete_data'))
                                    <form action="{{ route('admin.products.destroy', $product) }}" 
                                          method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this product? This will also delete all variants.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">🗑️</button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="7" class="no-data">
                            <div class="empty-state">
                                <div class="empty-icon">📦</div>
                                <h3>No products found</h3>
                                <p>Start by creating your first product group.</p>
                                @if($admin->hasPermission('manage_products'))
                                    <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
                                        ➕ Create First Product
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($productGroups->hasPages())
        <div class="pagination-wrapper">
            {{ $productGroups->withQueryString()->links() }}
        </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
            <div class="stat-number">{{ $productGroups->total() }}</div>
            <div class="stat-label">Total Products</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
            <div class="stat-number">{{ $productGroups->where('is_active', true)->count() }}</div>
            <div class="stat-label">Active Products</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">🔥</div>
        <div class="stat-info">
            <div class="stat-number">{{ $productGroups->where('is_trending', true)->count() }}</div>
            <div class="stat-label">Trending Products</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">🎨</div>
        <div class="stat-info">
            <div class="stat-number">{{ $productGroups->sum(function($p) { return $p->products->count(); }) }}</div>
            <div class="stat-label">Total Variants</div>
        </div>
    </div>
</div>
@endsection
