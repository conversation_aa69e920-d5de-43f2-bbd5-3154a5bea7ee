@extends('layouts.main')

@section('title', $product->name . ' - E-Commerce Store')

@section('content')
<div class="container">
    <!-- Breadcrumb -->
    <nav class="breadcrumb">
        <a href="{{ url('/') }}">Home</a>
        <span class="separator">›</span>
        <a href="{{ url('/products') }}">Products</a>
        <span class="separator">›</span>
        <a href="{{ url('/products?category=' . $product->category_id) }}">{{ $product->category->name }}</a>
        <span class="separator">›</span>
        <span class="current">{{ $product->name }}</span>
    </nav>

    <div class="product-layout">
        <!-- Product Images -->
        <div class="product-images">
            <div class="main-image">
                @if($product->main_image)
                    <img src="{{ $product->main_image }}" alt="{{ $product->name }}" id="mainImage">
                @else
                    <div class="no-image">📦</div>
                @endif
                <div class="image-badges">
                    @if($product->is_trending)
                        <span class="badge trending">🔥 Trending</span>
                    @endif
                </div>
            </div>
            
            @if($product->products->where('image', '!=', null)->count() > 0)
                <div class="thumbnail-images">
                    @if($product->main_image)
                        <img src="{{ $product->main_image }}" alt="{{ $product->name }}" 
                             class="thumbnail active" onclick="changeMainImage(this.src)">
                    @endif
                    @foreach($product->products->where('image', '!=', null)->unique('image') as $variant)
                        <img src="{{ $variant->image }}" alt="{{ $variant->color }} {{ $variant->size }}" 
                             class="thumbnail" onclick="changeMainImage(this.src)">
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Product Info -->
        <div class="product-info">
            <div class="product-header">
                <div class="product-category">{{ $product->category->name }}</div>
                <h1 class="product-title">{{ $product->name }}</h1>
                
                @if($product->rating)
                    <div class="product-rating">
                        @for($i = 1; $i <= 5; $i++)
                            <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                        @endfor
                        <span class="rating-text">({{ number_format($product->rating, 1) }} stars)</span>
                    </div>
                @endif

                <div class="product-price">
                    <span class="current-price" id="currentPrice">${{ number_format($product->base_price, 2) }}</span>
                    @if($product->products->count() > 1)
                        <span class="price-range">
                            - ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
                        </span>
                    @endif
                </div>
            </div>

            <div class="product-description">
                <h3>Description</h3>
                <p>{{ $product->description }}</p>
            </div>

            @if($product->tags->count() > 0)
                <div class="product-tags">
                    <h4>Tags</h4>
                    <div class="tags-list">
                        @foreach($product->tags as $tag)
                            <a href="{{ url('/products?tag=' . $tag->id) }}" class="tag">{{ $tag->name }}</a>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Variant Selection -->
            @if($product->products->count() > 0)
                <div class="variant-selection">
                    <h3>Select Variant</h3>
                    
                    <!-- Color Selection -->
                    @if($product->products->unique('color')->count() > 1)
                        <div class="variant-group">
                            <label class="variant-label">Color:</label>
                            <div class="color-options">
                                @foreach($product->products->unique('color') as $variant)
                                    <button class="color-option" 
                                            data-color="{{ $variant->color }}"
                                            onclick="selectColor('{{ $variant->color }}')"
                                            title="{{ $variant->color }}">
                                        @if($variant->image)
                                            <img src="{{ $variant->image }}" alt="{{ $variant->color }}">
                                        @else
                                            <div class="color-swatch" style="background-color: {{ strtolower($variant->color) }}"></div>
                                        @endif
                                        <span class="color-name">{{ $variant->color }}</span>
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Size Selection -->
                    @if($product->products->unique('size')->count() > 1)
                        <div class="variant-group">
                            <label class="variant-label">Size:</label>
                            <div class="size-options">
                                @foreach($product->products->unique('size') as $variant)
                                    <button class="size-option" 
                                            data-size="{{ $variant->size }}"
                                            onclick="selectSize('{{ $variant->size }}')">
                                        {{ $variant->size }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Selected Variant Info -->
                    <div class="selected-variant" id="selectedVariant" style="display: none;">
                        <div class="variant-info">
                            <span class="variant-name" id="variantName"></span>
                            <span class="variant-price" id="variantPrice"></span>
                        </div>
                        <div class="variant-stock">
                            <span class="stock-status" id="stockStatus"></span>
                            <span class="stock-quantity" id="stockQuantity"></span>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Quantity and Actions -->
            <div class="product-actions">
                <div class="quantity-selector">
                    <label for="quantity">Quantity:</label>
                    <div class="quantity-controls">
                        <button type="button" onclick="changeQuantity(-1)">-</button>
                        <input type="number" id="quantity" value="1" min="1" max="10">
                        <button type="button" onclick="changeQuantity(1)">+</button>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary btn-large" onclick="addToCart()" id="addToCartBtn">
                        🛒 Add to Cart
                    </button>
                    <button class="btn btn-outline" onclick="addToWishlist({{ $product->id }})">
                        {{ $inWishlist ? '❤️ In Wishlist' : '🤍 Add to Wishlist' }}
                    </button>
                </div>
            </div>

            <!-- Product Features -->
            <div class="product-features">
                <div class="feature">
                    <span class="feature-icon">🚚</span>
                    <div class="feature-text">
                        <strong>Free Shipping</strong>
                        <small>On orders over $50</small>
                    </div>
                </div>
                <div class="feature">
                    <span class="feature-icon">↩️</span>
                    <div class="feature-text">
                        <strong>Easy Returns</strong>
                        <small>30-day return policy</small>
                    </div>
                </div>
                <div class="feature">
                    <span class="feature-icon">🔒</span>
                    <div class="feature-text">
                        <strong>Secure Payment</strong>
                        <small>SSL encrypted checkout</small>
                    </div>
                </div>
                <div class="feature">
                    <span class="feature-icon">📞</span>
                    <div class="feature-text">
                        <strong>24/7 Support</strong>
                        <small>Customer service</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    @if($relatedProducts->count() > 0)
        <section class="related-products">
            <h2 class="section-title">Related Products</h2>
            <div class="products-grid">
                @foreach($relatedProducts as $relatedProduct)
                    <div class="product-card">
                        <div class="product-image">
                            @if($relatedProduct->main_image)
                                <img src="{{ $relatedProduct->main_image }}" alt="{{ $relatedProduct->name }}" loading="lazy">
                            @else
                                <div class="no-image">📦</div>
                            @endif
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">
                                <a href="{{ url('/products/' . $relatedProduct->id) }}">{{ $relatedProduct->name }}</a>
                            </h3>
                            <div class="product-price">
                                <span class="price">${{ number_format($relatedProduct->base_price, 2) }}</span>
                            </div>
                            <div class="product-actions">
                                <a href="{{ url('/products/' . $relatedProduct->id) }}" class="btn btn-primary btn-sm">View Details</a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </section>
    @endif

    <!-- Similar Products -->
    @if($similarProducts->count() > 0)
        <section class="similar-products">
            <h2 class="section-title">You Might Also Like</h2>
            <div class="products-grid">
                @foreach($similarProducts as $similarProduct)
                    <div class="product-card">
                        <div class="product-image">
                            @if($similarProduct->main_image)
                                <img src="{{ $similarProduct->main_image }}" alt="{{ $similarProduct->name }}" loading="lazy">
                            @else
                                <div class="no-image">📦</div>
                            @endif
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">
                                <a href="{{ url('/products/' . $similarProduct->id) }}">{{ $similarProduct->name }}</a>
                            </h3>
                            <div class="product-price">
                                <span class="price">${{ number_format($similarProduct->base_price, 2) }}</span>
                            </div>
                            <div class="product-actions">
                                <a href="{{ url('/products/' . $similarProduct->id) }}" class="btn btn-primary btn-sm">View Details</a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </section>
    @endif
</div>

<!-- Hidden data for JavaScript -->
<script type="application/json" id="productData">
{!! json_encode([
    'id' => $product->id,
    'base_price' => $product->base_price,
    'variants' => $product->products->map(function($variant) {
        return [
            'id' => $variant->id,
            'color' => $variant->color,
            'size' => $variant->size,
            'price_adjustment' => $variant->price_adjustment,
            'quantity' => $variant->quantity,
            'is_available' => $variant->is_available,
            'image' => $variant->image,
            'sku' => $variant->sku
        ];
    })
]) !!}
</script>

@push('styles')
<style>
    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .breadcrumb a {
        color: #3b82f6;
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .separator {
        color: #d1d5db;
    }

    .current {
        color: #374151;
        font-weight: 500;
    }

    .product-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        margin-bottom: 4rem;
    }

    /* Product Images */
    .product-images {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .main-image {
        position: relative;
        aspect-ratio: 1;
        border-radius: 12px;
        overflow: hidden;
        background: #f9fafb;
    }

    .main-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 4rem;
        color: #d1d5db;
    }

    .image-badges {
        position: absolute;
        top: 1rem;
        left: 1rem;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .badge.trending {
        background: #fbbf24;
        color: #92400e;
    }

    .thumbnail-images {
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
    }

    .thumbnail {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        border: 2px solid transparent;
        transition: all 0.3s ease;
    }

    .thumbnail:hover,
    .thumbnail.active {
        border-color: #3b82f6;
    }

    /* Product Info */
    .product-header {
        margin-bottom: 2rem;
    }

    .product-category {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .product-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .star {
        font-size: 1rem;
        color: #d1d5db;
    }

    .star.filled {
        color: #fbbf24;
    }

    .rating-text {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .current-price {
        font-size: 2rem;
        font-weight: 700;
        color: #059669;
    }

    .price-range {
        font-size: 1.125rem;
        color: #6b7280;
    }

    .product-description {
        margin-bottom: 2rem;
    }

    .product-description h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1rem;
    }

    .product-description p {
        color: #6b7280;
        line-height: 1.6;
    }

    .product-tags {
        margin-bottom: 2rem;
    }

    .product-tags h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.75rem;
    }

    .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .tag {
        background: #f3f4f6;
        color: #374151;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .tag:hover {
        background: #3b82f6;
        color: white;
    }

    /* Variant Selection */
    .variant-selection {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f9fafb;
        border-radius: 12px;
    }

    .variant-selection h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1.5rem;
    }

    .variant-group {
        margin-bottom: 1.5rem;
    }

    .variant-label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.75rem;
    }

    .color-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .color-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .color-option:hover,
    .color-option.selected {
        border-color: #3b82f6;
        background: #eff6ff;
    }

    .color-option img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 4px;
    }

    .color-swatch {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        border: 1px solid #d1d5db;
    }

    .color-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
    }

    .size-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .size-option {
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .size-option:hover,
    .size-option.selected {
        border-color: #3b82f6;
        background: #eff6ff;
        color: #3b82f6;
    }

    .selected-variant {
        padding: 1rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .variant-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .variant-name {
        font-weight: 600;
        color: #1f2937;
    }

    .variant-price {
        font-size: 1.25rem;
        font-weight: 700;
        color: #059669;
    }

    .variant-stock {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stock-status {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .stock-status.in-stock {
        color: #059669;
    }

    .stock-status.out-of-stock {
        color: #dc2626;
    }

    .stock-quantity {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* Product Actions */
    .product-actions {
        margin-bottom: 2rem;
    }

    .quantity-selector {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .quantity-selector label {
        font-weight: 500;
        color: #374151;
    }

    .quantity-controls {
        display: flex;
        align-items: center;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        overflow: hidden;
    }

    .quantity-controls button {
        padding: 0.5rem 0.75rem;
        border: none;
        background: #f9fafb;
        cursor: pointer;
        font-weight: 500;
        transition: background 0.3s ease;
    }

    .quantity-controls button:hover {
        background: #e5e7eb;
    }

    .quantity-controls input {
        width: 60px;
        padding: 0.5rem;
        border: none;
        text-align: center;
        font-weight: 500;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1.125rem;
        flex: 1;
    }

    /* Product Features */
    .product-features {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        padding: 1.5rem;
        background: #f9fafb;
        border-radius: 12px;
    }

    .feature {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .feature-icon {
        font-size: 1.5rem;
    }

    .feature-text strong {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #1f2937;
    }

    .feature-text small {
        font-size: 0.75rem;
        color: #6b7280;
    }

    /* Related/Similar Products */
    .related-products,
    .similar-products {
        margin-bottom: 4rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 2rem;
        text-align: center;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .product-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .product-card .product-image {
        height: 200px;
        overflow: hidden;
    }

    .product-card .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .product-card .product-info {
        padding: 1.5rem;
    }

    .product-card .product-name {
        margin-bottom: 1rem;
    }

    .product-card .product-name a {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .product-card .product-name a:hover {
        color: #3b82f6;
    }

    .product-card .product-price {
        margin-bottom: 1rem;
    }

    .product-card .price {
        font-size: 1.25rem;
        font-weight: 700;
        color: #059669;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .product-layout {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        .product-title {
            font-size: 1.5rem;
        }

        .current-price {
            font-size: 1.5rem;
        }

        .action-buttons {
            flex-direction: column;
        }

        .product-features {
            grid-template-columns: 1fr;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
    }
</style>
@endpush

@push('scripts')
<script>
    const productData = JSON.parse(document.getElementById('productData').textContent);
    let selectedColor = null;
    let selectedSize = null;
    let selectedVariant = null;

    function changeMainImage(src) {
        document.getElementById('mainImage').src = src;
        
        // Update thumbnail active state
        document.querySelectorAll('.thumbnail').forEach(thumb => {
            thumb.classList.remove('active');
            if (thumb.src === src) {
                thumb.classList.add('active');
            }
        });
    }

    function selectColor(color) {
        selectedColor = color;
        
        // Update UI
        document.querySelectorAll('.color-option').forEach(option => {
            option.classList.remove('selected');
            if (option.dataset.color === color) {
                option.classList.add('selected');
            }
        });
        
        updateSelectedVariant();
    }

    function selectSize(size) {
        selectedSize = size;
        
        // Update UI
        document.querySelectorAll('.size-option').forEach(option => {
            option.classList.remove('selected');
            if (option.dataset.size === size) {
                option.classList.add('selected');
            }
        });
        
        updateSelectedVariant();
    }

    function updateSelectedVariant() {
        if (!selectedColor && !selectedSize) {
            document.getElementById('selectedVariant').style.display = 'none';
            return;
        }
        
        // Find matching variant
        selectedVariant = productData.variants.find(variant => {
            return (!selectedColor || variant.color === selectedColor) &&
                   (!selectedSize || variant.size === selectedSize);
        });
        
        if (selectedVariant) {
            const finalPrice = productData.base_price + selectedVariant.price_adjustment;
            const variantName = `${selectedVariant.color} - ${selectedVariant.size}`;
            
            document.getElementById('variantName').textContent = variantName;
            document.getElementById('variantPrice').textContent = `$${finalPrice.toFixed(2)}`;
            document.getElementById('currentPrice').textContent = `$${finalPrice.toFixed(2)}`;
            
            // Update stock status
            const stockStatus = document.getElementById('stockStatus');
            const stockQuantity = document.getElementById('stockQuantity');
            
            if (selectedVariant.is_available && selectedVariant.quantity > 0) {
                stockStatus.textContent = '✅ In Stock';
                stockStatus.className = 'stock-status in-stock';
                stockQuantity.textContent = `${selectedVariant.quantity} available`;
                document.getElementById('addToCartBtn').disabled = false;
                document.getElementById('quantity').max = Math.min(selectedVariant.quantity, 10);
            } else {
                stockStatus.textContent = '❌ Out of Stock';
                stockStatus.className = 'stock-status out-of-stock';
                stockQuantity.textContent = 'Not available';
                document.getElementById('addToCartBtn').disabled = true;
            }
            
            document.getElementById('selectedVariant').style.display = 'block';
            
            // Update main image if variant has image
            if (selectedVariant.image) {
                changeMainImage(selectedVariant.image);
            }
        }
    }

    function changeQuantity(delta) {
        const quantityInput = document.getElementById('quantity');
        const currentValue = parseInt(quantityInput.value);
        const newValue = Math.max(1, Math.min(parseInt(quantityInput.max) || 10, currentValue + delta));
        quantityInput.value = newValue;
    }

    function addToCart() {
        @auth
            const quantity = parseInt(document.getElementById('quantity').value);
            const variantId = selectedVariant ? selectedVariant.id : null;
            
            if (productData.variants.length > 0 && !selectedVariant) {
                alert('Please select a variant first');
                return;
            }
            
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    product_id: productData.id,
                    variant_id: variantId,
                    quantity: quantity
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Added to cart!');
                    updateCartBadge(data.cart_count);
                } else {
                    alert('❌ ' + (data.message || 'Failed to add to cart'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Failed to add to cart');
            });
        @else
            alert('Please login to add items to your cart');
            window.location.href = '/login';
        @endauth
    }

    function addToWishlist(productId) {
        @auth
            fetch('/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ product_id: productId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Added to wishlist!');
                } else {
                    alert('❌ ' + (data.message || 'Failed to add to wishlist'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Failed to add to wishlist');
            });
        @else
            alert('Please login to add items to your wishlist');
            window.location.href = '/login';
        @endauth
    }

    // Initialize first variant if only one option
    document.addEventListener('DOMContentLoaded', function() {
        const colors = document.querySelectorAll('.color-option');
        const sizes = document.querySelectorAll('.size-option');
        
        if (colors.length === 1) {
            selectColor(colors[0].dataset.color);
        }
        
        if (sizes.length === 1) {
            selectSize(sizes[0].dataset.size);
        }
    });
</script>
@endpush
@endsection
