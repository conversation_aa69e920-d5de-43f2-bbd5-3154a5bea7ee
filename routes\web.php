<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\ProductVariantController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\TagController;
use App\Http\Controllers\Admin\ImageController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;

// User-facing routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/products', [HomeController::class, 'products'])->name('products');
Route::get('/products/{product}', [HomeController::class, 'productDetails'])->name('product.details');
Route::get('/search', [HomeController::class, 'search'])->name('search');

// User Profile Routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'index'])->name('profile');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [ProfileController::class, 'password'])->name('profile.password');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::get('/profile/settings', [ProfileController::class, 'settings'])->name('profile.settings');
    Route::put('/profile/settings', [ProfileController::class, 'updateSettings'])->name('profile.settings.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login Routes
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);

    // Register Routes
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [RegisterController::class, 'register']);
});

// Protected Routes
Route::middleware('auth')->group(function () {
    // User Dashboard (if needed in future)
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('user.dashboard');

    // Logout
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');
});

// Debug route (remove in production)
Route::get('/debug-login', function () {
    return view('debug-login');
});

Route::post('/debug-login', function (Illuminate\Http\Request $request) {
    $email = $request->email;
    $password = $request->password;

    $user = \App\Models\User::where('email', $email)->first();

    $result = [
        'email' => $email,
        'user_found' => $user ? true : false,
        'user_name' => $user ? $user->name : null,
        'password_correct' => $user ? \Illuminate\Support\Facades\Hash::check($password, $user->password) : false,
        'auth_attempt' => false,
    ];

    if ($user && $result['password_correct']) {
        $result['auth_attempt'] = \Illuminate\Support\Facades\Auth::attempt(['email' => $email, 'password' => $password]);
        if ($result['auth_attempt']) {
            \Illuminate\Support\Facades\Auth::logout();
        }
    }

    return response()->json($result);
});

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Redirect admin login to main login page
    Route::get('/login', function () {
        return redirect()->route('login')->with('info', 'استخدم نفس صفحة تسجيل الدخول للإدارة والمستخدمين العاديين.');
    })->name('login');

    // Admin Dashboard Route (outside middleware for route definition)
    Route::get('/dashboard', [AdminAuthController::class, 'dashboard'])->name('dashboard')->middleware('auth:admin');

    // Protected admin routes
    Route::middleware('auth:admin')->group(function () {

        // Product Management
        Route::resource('products', ProductController::class);
        Route::patch('/products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');
        Route::patch('/products/{product}/toggle-trending', [ProductController::class, 'toggleTrending'])->name('products.toggle-trending');

        // Category Management
        Route::resource('categories', CategoryController::class);
        Route::patch('/categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
        Route::get('/categories-stats', [CategoryController::class, 'stats'])->name('categories.stats');

        // Tag Management
        Route::resource('tags', TagController::class);
        Route::post('/tags/bulk-create', [TagController::class, 'bulkCreate'])->name('tags.bulk-create');
        Route::delete('/tags/clean-unused', [TagController::class, 'cleanUnused'])->name('tags.clean-unused');
        Route::get('/tags-stats', [TagController::class, 'stats'])->name('tags.stats');

        // Image Management
        Route::post('/images/upload', [ImageController::class, 'upload'])->name('images.upload');
        Route::delete('/images/delete', [ImageController::class, 'delete'])->name('images.delete');
        Route::get('/images', [ImageController::class, 'index'])->name('images.index');
        Route::get('/images/gallery', [ImageController::class, 'gallery'])->name('images.gallery');
        Route::post('/images/resize', [ImageController::class, 'resize'])->name('images.resize');

        // Product Variants Management
        Route::prefix('products/{productGroup}')->name('products.')->group(function () {
            Route::get('/variants', [ProductVariantController::class, 'index'])->name('variants.index');
            Route::get('/variants/create', [ProductVariantController::class, 'create'])->name('variants.create');
            Route::post('/variants', [ProductVariantController::class, 'store'])->name('variants.store');
            Route::get('/variants/{variant}/edit', [ProductVariantController::class, 'edit'])->name('variants.edit');
            Route::put('/variants/{variant}', [ProductVariantController::class, 'update'])->name('variants.update');
            Route::delete('/variants/{variant}', [ProductVariantController::class, 'destroy'])->name('variants.destroy');
            Route::patch('/variants/{variant}/toggle-availability', [ProductVariantController::class, 'toggleAvailability'])->name('variants.toggle-availability');
            Route::patch('/variants/bulk-update-stock', [ProductVariantController::class, 'bulkUpdateStock'])->name('variants.bulk-update-stock');
        });
    });
});

