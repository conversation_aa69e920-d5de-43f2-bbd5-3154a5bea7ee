<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin
        Admin::create([
            'name' => 'Super Administrator',
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+20123456789',
            'role' => 'super_admin',
            'permissions' => [
                'manage_users',
                'manage_admins',
                'manage_products',
                'manage_categories',
                'manage_tags',
                'manage_orders',
                'view_analytics',
                'manage_settings',
                'manage_permissions',
                'delete_data',
                'export_data',
                'import_data'
            ],
            'notes' => 'Default super administrator account',
            'is_active' => true,
            'can_login' => true,
        ]);

        // Create Regular Admin
        Admin::create([
            'name' => 'Store Manager',
            'first_name' => 'Store',
            'last_name' => 'Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+***********',
            'role' => 'admin',
            'permissions' => [
                'manage_products',
                'manage_categories',
                'manage_tags',
                'manage_orders',
                'view_analytics',
            ],
            'notes' => 'Store manager with product management permissions',
            'is_active' => true,
            'can_login' => true,
            'created_by' => 1, // Created by super admin
        ]);

        // Create Moderator
        Admin::create([
            'name' => 'Content Moderator',
            'first_name' => 'Content',
            'last_name' => 'Moderator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+20123456787',
            'role' => 'moderator',
            'permissions' => [
                'manage_products',
                'manage_categories',
                'view_analytics',
            ],
            'notes' => 'Content moderator with limited permissions',
            'is_active' => true,
            'can_login' => true,
            'created_by' => 1, // Created by super admin
        ]);

        // Create Editor
        Admin::create([
            'name' => 'Content Editor',
            'first_name' => 'Content',
            'last_name' => 'Editor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'phone' => '+20123456786',
            'role' => 'editor',
            'permissions' => [
                'manage_products',
                'manage_categories',
            ],
            'notes' => 'Content editor with basic permissions',
            'is_active' => true,
            'can_login' => true,
            'created_by' => 1, // Created by super admin
        ]);

        $this->command->info('Admin users created successfully!');
        $this->command->info('Super Admin: <EMAIL> / password123');
        $this->command->info('Manager: <EMAIL> / password123');
        $this->command->info('Moderator: <EMAIL> / password123');
        $this->command->info('Editor: <EMAIL> / password123');
    }
}
