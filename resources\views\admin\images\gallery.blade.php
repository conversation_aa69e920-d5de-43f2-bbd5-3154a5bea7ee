@extends('admin.layouts.app')

@section('title', 'Image Gallery')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">🖼️ Image Gallery</h1>
            <p class="page-subtitle">Manage uploaded product images</p>
        </div>
        <div class="header-actions">
            <button onclick="showUploadModal()" class="btn btn-primary">
                📷 Upload Images
            </button>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline">
                ← Back to Products
            </a>
        </div>
    </div>
</div>

<!-- Image Type Filter -->
<div class="filters-card">
    <div class="filter-tabs">
        <button class="filter-tab active" onclick="switchType('product')" id="product-tab">
            📦 Product Images
        </button>
        <button class="filter-tab" onclick="switchType('variant')" id="variant-tab">
            🎨 Variant Images
        </button>
    </div>
</div>

<!-- Images Grid -->
<div class="images-container">
    <div class="images-header">
        <h3 id="images-title">Product Images</h3>
        <div class="view-options">
            <button onclick="toggleView('grid')" class="view-btn active" id="grid-btn">🔲</button>
            <button onclick="toggleView('list')" class="view-btn" id="list-btn">📋</button>
        </div>
    </div>
    
    <div id="images-grid" class="images-grid">
        <!-- Images will be loaded here via JavaScript -->
    </div>
    
    <div id="loading" class="loading-state">
        <div class="loading-spinner">⏳</div>
        <p>Loading images...</p>
    </div>
    
    <div id="empty-state" class="empty-state" style="display: none;">
        <div class="empty-icon">🖼️</div>
        <h3>No images found</h3>
        <p>Upload your first product image to get started.</p>
        <button onclick="showUploadModal()" class="btn btn-primary">
            📷 Upload First Image
        </button>
    </div>
</div>

<!-- Upload Modal -->
<div id="uploadModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📷 Upload Images</h3>
            <button onclick="hideUploadModal()" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
            <div class="upload-area" id="uploadArea">
                <div class="upload-placeholder">
                    <div class="upload-icon">📷</div>
                    <p>Click to select images or drag & drop</p>
                    <small>Supports: JPEG, PNG, GIF, WebP (Max: 5MB each)</small>
                </div>
            </div>
            <input type="file" id="fileInput" multiple accept="image/*" style="display: none;">
            
            <div class="upload-progress" id="uploadProgress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p id="progressText">Uploading...</p>
            </div>
        </div>
        <div class="modal-footer">
            <button onclick="hideUploadModal()" class="btn btn-outline">Close</button>
        </div>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="previewModal" class="modal" style="display: none;">
    <div class="modal-content large">
        <div class="modal-header">
            <h3 id="previewTitle">Image Preview</h3>
            <button onclick="hidePreviewModal()" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
            <div class="image-preview-container">
                <img id="previewImage" src="" alt="Preview">
            </div>
            <div class="image-details">
                <div class="detail-item">
                    <span class="detail-label">Filename:</span>
                    <span class="detail-value" id="previewFilename"></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Size:</span>
                    <span class="detail-value" id="previewSize"></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">URL:</span>
                    <input type="text" id="previewUrl" class="form-control" readonly>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button onclick="copyImageUrl()" class="btn btn-secondary">📋 Copy URL</button>
            <button onclick="deleteImage()" class="btn btn-danger">🗑️ Delete</button>
            <button onclick="hidePreviewModal()" class="btn btn-outline">Close</button>
        </div>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .filters-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .filter-tabs {
        display: flex;
        gap: 1rem;
    }

    .filter-tab {
        padding: 0.75rem 1.5rem;
        border: 2px solid #e9ecef;
        background: white;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .filter-tab.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .images-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .images-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .view-options {
        display: flex;
        gap: 0.5rem;
    }

    .view-btn {
        padding: 0.5rem;
        border: 1px solid #e9ecef;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .view-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .image-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .image-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .image-card img {
        width: 100%;
        height: 150px;
        object-fit: cover;
    }

    .image-info {
        padding: 1rem;
    }

    .image-filename {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        word-break: break-all;
    }

    .image-size {
        color: #666;
        font-size: 0.8rem;
    }

    .loading-state, .empty-state {
        text-align: center;
        padding: 3rem;
        color: #666;
    }

    .loading-spinner {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-content.large {
        max-width: 800px;
    }

    .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .upload-area {
        border: 2px dashed #ddd;
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .upload-area:hover, .upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .upload-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .upload-progress {
        margin-top: 1rem;
    }

    .progress-bar {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        background: #667eea;
        transition: width 0.3s ease;
        width: 0%;
    }

    .image-preview-container {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .image-preview-container img {
        max-width: 100%;
        max-height: 400px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .image-details {
        display: grid;
        gap: 1rem;
    }

    .detail-item {
        display: grid;
        grid-template-columns: 100px 1fr;
        gap: 1rem;
        align-items: center;
    }

    .detail-label {
        font-weight: 500;
        color: #666;
    }

    .detail-value {
        color: #333;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .images-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }

        .filter-tabs {
            flex-direction: column;
        }

        .detail-item {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@push('scripts')
<script>
let currentType = 'product';
let currentImages = [];
let selectedImage = null;

document.addEventListener('DOMContentLoaded', function() {
    loadImages();
    setupUploadArea();
});

function switchType(type) {
    currentType = type;
    
    // Update tabs
    document.querySelectorAll('.filter-tab').forEach(tab => tab.classList.remove('active'));
    document.getElementById(type + '-tab').classList.add('active');
    
    // Update title
    document.getElementById('images-title').textContent = type === 'product' ? 'Product Images' : 'Variant Images';
    
    // Load images
    loadImages();
}

function loadImages() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('images-grid').innerHTML = '';
    document.getElementById('empty-state').style.display = 'none';
    
    fetch(`/admin/images?type=${currentType}`, {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('loading').style.display = 'none';
        
        if (data.success) {
            currentImages = data.images;
            displayImages(data.images);
        } else {
            console.error('Failed to load images:', data.error);
        }
    })
    .catch(error => {
        document.getElementById('loading').style.display = 'none';
        console.error('Error loading images:', error);
    });
}

function displayImages(images) {
    const grid = document.getElementById('images-grid');
    
    if (images.length === 0) {
        document.getElementById('empty-state').style.display = 'block';
        return;
    }
    
    grid.innerHTML = images.map(image => `
        <div class="image-card" onclick="showImagePreview('${image.url}', '${image.filename}', '${formatFileSize(image.size)}', '${image.path}')">
            <img src="${image.url}" alt="${image.filename}" loading="lazy">
            <div class="image-info">
                <div class="image-filename">${image.filename}</div>
                <div class="image-size">${formatFileSize(image.size)}</div>
            </div>
        </div>
    `).join('');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showImagePreview(url, filename, size, path) {
    selectedImage = { url, filename, size, path };
    
    document.getElementById('previewImage').src = url;
    document.getElementById('previewFilename').textContent = filename;
    document.getElementById('previewSize').textContent = size;
    document.getElementById('previewUrl').value = url;
    document.getElementById('previewModal').style.display = 'flex';
}

function hideImagePreview() {
    document.getElementById('previewModal').style.display = 'none';
    selectedImage = null;
}

function copyImageUrl() {
    const urlInput = document.getElementById('previewUrl');
    urlInput.select();
    document.execCommand('copy');
    alert('Image URL copied to clipboard!');
}

function deleteImage() {
    if (!selectedImage || !confirm('Are you sure you want to delete this image?')) {
        return;
    }
    
    fetch('/admin/images/delete', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ path: selectedImage.path })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            hideImagePreview();
            loadImages();
            alert('Image deleted successfully!');
        } else {
            alert('Failed to delete image: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Delete error:', error);
        alert('Failed to delete image. Please try again.');
    });
}

function showUploadModal() {
    document.getElementById('uploadModal').style.display = 'flex';
}

function hideUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
    document.getElementById('uploadProgress').style.display = 'none';
    document.getElementById('progressFill').style.width = '0%';
}

function setupUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            uploadFiles(Array.from(e.target.files));
        }
    });
    
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
        if (files.length > 0) {
            uploadFiles(files);
        }
    });
}

function uploadFiles(files) {
    document.getElementById('uploadProgress').style.display = 'block';
    
    let completed = 0;
    const total = files.length;
    
    files.forEach((file, index) => {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', currentType);
        
        fetch('/admin/images/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            completed++;
            const progress = (completed / total) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `Uploading... ${completed}/${total}`;
            
            if (completed === total) {
                setTimeout(() => {
                    hideUploadModal();
                    loadImages();
                }, 1000);
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            completed++;
        });
    });
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
    }
});
</script>
@endpush
@endsection
