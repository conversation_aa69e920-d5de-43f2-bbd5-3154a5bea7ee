@extends('admin.layouts.app')

@section('title', 'View Product')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">👁️ {{ $product->name }}</h1>
            <p class="page-subtitle">Product details and management</p>
        </div>
        <div class="header-actions">
            @if($admin->hasPermission('manage_products'))
                <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                    ✏️ Edit Product
                </a>
                <a href="{{ route('admin.products.variants.index', $product) }}" class="btn btn-secondary">
                    🎨 Manage Variants
                </a>
            @endif
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline">
                ← Back to Products
            </a>
        </div>
    </div>
</div>

<!-- Product Overview -->
<div class="product-overview">
    <div class="product-main">
        <div class="product-image-section">
            @if($product->main_image)
                <img src="{{ $product->main_image }}" alt="{{ $product->name }}" class="product-image">
            @else
                <div class="no-image-placeholder">
                    <div class="placeholder-icon">📦</div>
                    <p>No image available</p>
                </div>
            @endif
        </div>
        
        <div class="product-details">
            <div class="product-header">
                <h2 class="product-name">{{ $product->name }}</h2>
                <div class="product-badges">
                    <span class="status-badge {{ $product->is_active ? 'active' : 'inactive' }}">
                        {{ $product->is_active ? 'Active' : 'Inactive' }}
                    </span>
                    @if($product->is_trending)
                        <span class="status-badge trending">🔥 Trending</span>
                    @endif
                </div>
            </div>
            
            <div class="product-meta">
                <div class="meta-item">
                    <span class="meta-label">Category:</span>
                    <span class="meta-value category-badge">{{ $product->category->name }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Base Price:</span>
                    <span class="meta-value price">${{ number_format($product->base_price, 2) }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Rating:</span>
                    <div class="rating">
                        @for($i = 1; $i <= 5; $i++)
                            <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                        @endfor
                        <span class="rating-text">({{ number_format($product->rating, 1) }})</span>
                    </div>
                </div>
            </div>
            
            <div class="product-description">
                <h4>Description</h4>
                <p>{{ $product->description }}</p>
            </div>
            
            @if($product->tags->count() > 0)
                <div class="product-tags">
                    <h4>Tags</h4>
                    <div class="tags-list">
                        @foreach($product->tags as $tag)
                            <span class="tag">{{ $tag->name }}</span>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Product Statistics -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon">🎨</div>
        <div class="stat-info">
            <div class="stat-number">{{ $product->products->count() }}</div>
            <div class="stat-label">Variants</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
            <div class="stat-number">{{ $product->products->sum('quantity') }}</div>
            <div class="stat-label">Total Stock</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">💰</div>
        <div class="stat-info">
            <div class="stat-number">
                ${{ number_format($product->products->min('price_adjustment') + $product->base_price, 2) }} - 
                ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
            </div>
            <div class="stat-label">Price Range</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
            <div class="stat-number">{{ $product->products->where('is_available', true)->count() }}</div>
            <div class="stat-label">Available Variants</div>
        </div>
    </div>
</div>

<!-- Product Variants -->
@if($product->products->count() > 0)
    <div class="variants-section">
        <div class="section-header">
            <h3>🎨 Product Variants</h3>
            @if($admin->hasPermission('manage_products'))
                <a href="{{ route('admin.products.variants.create', $product) }}" class="btn btn-primary btn-sm">
                    ➕ Add Variant
                </a>
            @endif
        </div>
        
        <div class="variants-grid">
            @foreach($product->products->take(8) as $variant)
                <div class="variant-card">
                    <div class="variant-header">
                        <h4>{{ $variant->color }} - {{ $variant->size }}</h4>
                        <span class="availability-badge {{ $variant->is_available ? 'available' : 'unavailable' }}">
                            {{ $variant->is_available ? 'Available' : 'Unavailable' }}
                        </span>
                    </div>
                    
                    <div class="variant-details">
                        <div class="variant-price">
                            ${{ number_format($product->base_price + $variant->price_adjustment, 2) }}
                            @if($variant->price_adjustment != 0)
                                <small class="price-adjustment">
                                    ({{ $variant->price_adjustment >= 0 ? '+' : '' }}${{ number_format($variant->price_adjustment, 2) }})
                                </small>
                            @endif
                        </div>
                        
                        <div class="variant-stock">
                            <span class="stock-label">Stock:</span>
                            <span class="stock-value {{ $variant->quantity <= 5 ? 'low-stock' : '' }}">
                                {{ $variant->quantity }}
                            </span>
                        </div>
                        
                        @if($variant->sku)
                            <div class="variant-sku">
                                <small>SKU: {{ $variant->sku }}</small>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        @if($product->products->count() > 8)
            <div class="view-all-variants">
                <a href="{{ route('admin.products.variants.index', $product) }}" class="btn btn-outline">
                    View All {{ $product->products->count() }} Variants →
                </a>
            </div>
        @endif
    </div>
@else
    <div class="no-variants">
        <div class="empty-state">
            <div class="empty-icon">🎨</div>
            <h3>No variants created</h3>
            <p>This product doesn't have any color/size variants yet.</p>
            @if($admin->hasPermission('manage_products'))
                <a href="{{ route('admin.products.variants.create', $product) }}" class="btn btn-primary">
                    ➕ Create First Variant
                </a>
            @endif
        </div>
    </div>
@endif

<!-- Product Information -->
<div class="info-section">
    <h3>📊 Product Information</h3>
    <div class="info-grid">
        <div class="info-item">
            <span class="info-label">Product ID:</span>
            <span class="info-value">{{ $product->id }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Created:</span>
            <span class="info-value">{{ $product->created_at->format('M d, Y H:i') }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Last Updated:</span>
            <span class="info-value">{{ $product->updated_at->format('M d, Y H:i') }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Category:</span>
            <span class="info-value">{{ $product->category->name }}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Tags Count:</span>
            <span class="info-value">{{ $product->tags->count() }} tags</span>
        </div>
        <div class="info-item">
            <span class="info-label">Status:</span>
            <span class="info-value">{{ $product->is_active ? 'Active' : 'Inactive' }}</span>
        </div>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .product-overview {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .product-main {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 2rem;
        padding: 2rem;
    }

    .product-image-section {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-image {
        width: 100%;
        max-width: 300px;
        height: 300px;
        object-fit: cover;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .no-image-placeholder {
        width: 300px;
        height: 300px;
        background: #f8f9fa;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #666;
        border: 2px dashed #ddd;
    }

    .placeholder-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .product-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .product-name {
        font-size: 2rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .product-badges {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .product-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .meta-label {
        font-weight: 500;
        color: #666;
    }

    .meta-value {
        font-weight: 600;
        color: #333;
    }

    .price {
        font-size: 1.25rem;
        color: #28a745;
    }

    .product-description {
        margin-bottom: 2rem;
    }

    .product-description h4,
    .product-tags h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.75rem;
    }

    .product-description p {
        color: #666;
        line-height: 1.6;
    }

    .tags-list {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }

    .tag {
        background: #e9ecef;
        color: #495057;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .variants-section, .info-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .section-header h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .variants-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .variant-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .variant-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .variant-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .variant-header h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .availability-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .availability-badge.available {
        background: #d4edda;
        color: #155724;
    }

    .availability-badge.unavailable {
        background: #f8d7da;
        color: #721c24;
    }

    .variant-price {
        font-size: 1.25rem;
        font-weight: 600;
        color: #28a745;
        margin-bottom: 0.5rem;
    }

    .price-adjustment {
        color: #666;
        font-size: 0.9rem;
    }

    .variant-stock {
        margin-bottom: 0.5rem;
    }

    .stock-label {
        color: #666;
        font-size: 0.9rem;
    }

    .stock-value {
        font-weight: 600;
        color: #333;
    }

    .stock-value.low-stock {
        color: #dc3545;
    }

    .variant-sku {
        color: #666;
        font-size: 0.8rem;
    }

    .view-all-variants {
        text-align: center;
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }

    .no-variants {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 3rem;
        margin-bottom: 2rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .info-label {
        font-weight: 500;
        color: #666;
    }

    .info-value {
        font-weight: 600;
        color: #333;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
        }

        .product-main {
            grid-template-columns: 1fr;
        }

        .product-meta {
            grid-template-columns: 1fr;
        }

        .variants-grid {
            grid-template-columns: 1fr;
        }

        .info-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush
@endsection
