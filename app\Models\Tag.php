<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Tag extends Model
{
    use HasFactory;
    protected $fillable = ['name'];

    public function productGroups() {
        return $this->belongsToMany(ProductGroup::class, 'product_group_tags');
    }

    // Get all products through product groups
    public function products() {
        return $this->hasManyThrough(Product::class, ProductGroup::class, 'id', 'product_group_id', 'id', 'id')
                    ->join('product_group_tags', 'product_groups.id', '=', 'product_group_tags.product_group_id')
                    ->where('product_group_tags.tag_id', $this->id);
    }
}
