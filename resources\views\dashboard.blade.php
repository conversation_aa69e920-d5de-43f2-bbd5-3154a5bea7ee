@extends('layouts.app')

@section('title', 'Dashboard')

@push('styles')
<style>
    .dashboard-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .dashboard-subtitle {
        color: #666;
        font-size: 1.1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        color: #666;
        font-size: 1rem;
        font-weight: 500;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .card-body {
        padding: 1.5rem;
    }

    .recent-products {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .product-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .product-item:last-child {
        border-bottom: none;
    }

    .product-info h4 {
        margin: 0 0 0.25rem 0;
        color: #333;
        font-size: 1rem;
    }

    .product-info p {
        margin: 0;
        color: #666;
        font-size: 0.875rem;
    }

    .product-price {
        font-weight: 600;
        color: #28a745;
        font-size: 1.1rem;
    }

    .quick-actions {
        display: grid;
        gap: 1rem;
    }

    .action-btn {
        display: block;
        padding: 1rem;
        background: #f8f9fa;
        color: #333;
        text-decoration: none;
        border-radius: 8px;
        text-align: center;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .action-btn:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .welcome-message {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 16px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .welcome-message h2 {
        margin: 0 0 0.5rem 0;
        font-size: 1.5rem;
    }

    .welcome-message p {
        margin: 0;
        opacity: 0.9;
    }

    @media (max-width: 768px) {
        .content-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }
</style>
@endpush

@section('content')
<div class="dashboard-header">
    <h1 class="dashboard-title">Dashboard</h1>
    <p class="dashboard-subtitle">Welcome back, {{ Auth::user()->name }}! Here's your store overview.</p>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <span class="stat-icon">📦</span>
        <div class="stat-number">{{ $stats['total_products'] }}</div>
        <div class="stat-label">Total Products</div>
    </div>

    <div class="stat-card">
        <span class="stat-icon">✅</span>
        <div class="stat-number">{{ $stats['active_products'] }}</div>
        <div class="stat-label">Active Products</div>
    </div>

    <div class="stat-card">
        <span class="stat-icon">🔥</span>
        <div class="stat-number">{{ $stats['trending_products'] }}</div>
        <div class="stat-label">Trending Products</div>
    </div>

    <div class="stat-card">
        <span class="stat-icon">📂</span>
        <div class="stat-number">{{ $stats['total_categories'] }}</div>
        <div class="stat-label">Categories</div>
    </div>

    <div class="stat-card">
        <span class="stat-icon">🏷️</span>
        <div class="stat-number">{{ $stats['total_tags'] }}</div>
        <div class="stat-label">Tags</div>
    </div>
</div>

<!-- Content Grid -->
<div class="content-grid">
    <!-- Recent Products -->
    <div class="card">
        <div class="card-header">
            Recent Products
        </div>
        <div class="card-body">
            @if($recent_products->count() > 0)
                <ul class="recent-products">
                    @foreach($recent_products as $product)
                        <li class="product-item">
                            <div class="product-info">
                                <h4>{{ $product->name }}</h4>
                                <p>{{ $product->category->name ?? 'No Category' }} • {{ $product->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="product-price">${{ number_format($product->base_price, 2) }}</div>
                        </li>
                    @endforeach
                </ul>
            @else
                <p>No products found. <a href="{{ url('/admin_panel.html') }}">Create your first product</a>!</p>
            @endif
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            Quick Actions
        </div>
        <div class="card-body">
            <div class="quick-actions">
                <a href="{{ url('/admin_panel.html') }}" class="action-btn">
                    🛍️ Manage Products
                </a>
                <a href="{{ url('/admin_panel.html') }}" class="action-btn">
                    📂 Manage Categories
                </a>
                <a href="{{ url('/admin_panel.html') }}" class="action-btn">
                    🏷️ Manage Tags
                </a>
                <a href="{{ url('/test_new_structure.html') }}" class="action-btn">
                    🧪 Test API
                </a>
                <a href="{{ route('logout') }}" class="action-btn" 
                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                    🚪 Logout
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Hidden logout form -->
<form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
    @csrf
</form>
@endsection
