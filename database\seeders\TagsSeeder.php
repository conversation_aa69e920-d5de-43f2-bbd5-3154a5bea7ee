<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Tag;

class TagsSeeder extends Seeder
{
    public function run(): void
    {
        $tags = [
            // Status Tags
            'New Arrival',
            'Best Seller',
            'Trending',
            'Limited Edition',
            'Exclusive',
            'Featured',

            // Discount Tags
            'Sale',
            'Discounted',
            'Clearance',
            'Hot Deal',
            'Flash Sale',

            // Style Tags
            'Casual',
            'Formal',
            'Vintage',
            'Modern',
            'Classic',
            'Minimalist',
            'Bohemian',
            'Streetwear',

            // Season Tags
            'Summer',
            'Winter',
            'Spring',
            'Fall',
            'All Season',

            // Material Tags
            'Cotton',
            'Silk',
            'Wool',
            'Leather',
            'Denim',
            'Polyester',
            'Organic',
            'Eco-Friendly',

            // Occasion Tags
            'Work',
            'Party',
            'Wedding',
            'Vacation',
            'Gym',
            'Outdoor',
            'Date Night',

            // Special Features
            'Waterproof',
            'Breathable',
            'Stretch',
            'Quick Dry',
            'UV Protection',
            'Anti-Wrinkle',
        ];

        foreach ($tags as $tag) {
            Tag::create(['name' => $tag]);
        }

        $this->command->info('Tags created successfully!');
    }
}
