<?php

// Test script to verify user accounts and passwords
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "🔍 Testing User Accounts and Passwords\n";
echo "=====================================\n\n";

try {
    // Get all users
    $users = User::all();
    
    if ($users->isEmpty()) {
        echo "❌ No users found in database!\n";
        echo "Run: php artisan db:seed --class=UsersSeeder\n";
        exit;
    }
    
    echo "📋 Found " . $users->count() . " users in database:\n\n";
    
    foreach ($users as $user) {
        echo "👤 User: {$user->name}\n";
        echo "   Email: {$user->email}\n";
        echo "   Password Hash: " . substr($user->password, 0, 20) . "...\n";
        
        // Test password verification
        $testPasswords = ['password', 'password123'];
        
        foreach ($testPasswords as $testPassword) {
            if (Hash::check($testPassword, $user->password)) {
                echo "   ✅ Password '{$testPassword}' is CORRECT\n";
                break;
            } else {
                echo "   ❌ Password '{$testPassword}' is incorrect\n";
            }
        }
        
        echo "\n";
    }
    
    echo "🧪 Testing Laravel Auth::attempt()\n";
    echo "==================================\n\n";
    
    // Test Auth::attempt with known credentials
    $testCredentials = [
        ['email' => '<EMAIL>', 'password' => 'password'],
        ['email' => '<EMAIL>', 'password' => 'password123'],
        ['email' => '<EMAIL>', 'password' => 'password123'],
    ];
    
    foreach ($testCredentials as $credentials) {
        echo "Testing: {$credentials['email']} / {$credentials['password']}\n";
        
        if (\Illuminate\Support\Facades\Auth::attempt($credentials)) {
            echo "✅ Auth::attempt() SUCCESS\n";
            \Illuminate\Support\Facades\Auth::logout();
        } else {
            echo "❌ Auth::attempt() FAILED\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
