# 🔧 Admin Model Fix - Method Conflict Resolved

## ✅ **Issue Fixed**

**Problem**: The `can()` method in Admin model conflicted with <PERSON><PERSON>'s built-in `can()` method from `Illuminate\Foundation\Auth\User`.

**Error**: 
```
Declaration of App\Models\Admin::can(string $action): bool must be compatible with Illuminate\Foundation\Auth\User::can($abilities, $arguments = [])
```

## 🔧 **Solution Applied**

### **Changed Method Name:**
```php
// ❌ Before (Conflicted with <PERSON><PERSON>'s can() method)
public function can(string $action): bool
{
    return $this->hasPermission($action);
}

// ✅ After (No conflict)
public function canPerform(string $action): bool
{
    return $this->hasPermission($action);
}
```

## 📋 **Admin Model Permission Methods**

### **Available Methods:**
```php
// Check specific permission
$admin->hasPermission('manage_products')

// Check any of multiple permissions
$admin->hasAnyPermission(['manage_products', 'view_analytics'])

// Check all permissions
$admin->hasAllPermissions(['manage_products', 'view_analytics'])

// Check if admin can perform action (our custom method)
$admin->canPerform('manage_products')

// Laravel's built-in authorization (still available)
$admin->can('some-gate-or-policy')
```

### **Usage Examples:**
```php
// In controllers or views
if ($admin->hasPermission('manage_products')) {
    // Allow product management
}

if ($admin->canPerform('delete_data')) {
    // Allow data deletion
}

// Role-based checks
if ($admin->role === 'super_admin') {
    // Super admin access
}

// Check if admin can login
if ($admin->canLogin()) {
    // Allow login
}
```

## 🚀 **Ready to Seed**

The Admin model is now fixed and ready. You can run:

```bash
# Run migrations
php artisan migrate:fresh

# Run all seeders
php artisan db:seed

# Or run specific seeder
php artisan db:seed --class=AdminSeeder
```

## 🎯 **Admin Accounts Created**

The seeder will create these accounts:

| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| **Super Admin** | <EMAIL> | password123 | All permissions |
| **Manager** | <EMAIL> | password123 | Product management |
| **Moderator** | <EMAIL> | password123 | Limited management |
| **Editor** | <EMAIL> | password123 | Basic editing |

## 🔐 **Permission System**

### **Available Permissions:**
- `manage_users` - User management
- `manage_admins` - Admin management
- `manage_products` - Product management
- `manage_categories` - Category management
- `manage_tags` - Tag management
- `manage_orders` - Order management
- `view_analytics` - View analytics
- `manage_settings` - System settings
- `manage_permissions` - Permission management
- `delete_data` - Delete data
- `export_data` - Export data
- `import_data` - Import data

### **Role Hierarchy:**
1. **Super Admin** - All permissions
2. **Admin** - Product, order, analytics management
3. **Moderator** - Product and category management
4. **Editor** - Basic product management

The Admin model is now **fully functional** and ready for use! 🎉
