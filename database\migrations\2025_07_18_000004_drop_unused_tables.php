<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop tables we no longer need with the simplified structure
        Schema::dropIfExists('product_variants');
        Schema::dropIfExists('colors');
        Schema::dropIfExists('sizes');
        Schema::dropIfExists('product_tags'); // Old pivot table
        Schema::dropIfExists('product_images'); // We'll use single image per variant
        Schema::dropIfExists('cart_items'); // Will need to recreate this later
        Schema::dropIfExists('wishlists'); // Will need to recreate this later
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the dropped tables if needed (basic structure)
        Schema::create('colors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('hex_code')->nullable();
            $table->timestamps();
        });

        Schema::create('sizes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('abbreviation')->nullable();
            $table->timestamps();
        });

        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('color_id')->constrained()->onDelete('cascade');
            $table->foreignId('size_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(0);
            $table->string('image')->nullable();
            $table->timestamps();
            
            $table->unique(['product_id', 'color_id', 'size_id']);
        });
    }
};
