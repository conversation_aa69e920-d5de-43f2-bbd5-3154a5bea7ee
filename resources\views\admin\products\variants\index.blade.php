@extends('admin.layouts.app')

@section('title', 'Product Variants')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">🎨 Product Variants</h1>
            <p class="page-subtitle">{{ $productGroup->name }} - Color & Size Variations</p>
        </div>
        <div class="header-actions">
            @if($admin->hasPermission('manage_products'))
                <a href="{{ route('admin.products.variants.create', $productGroup) }}" class="btn btn-primary">
                    ➕ Add Variant
                </a>
            @endif
            <a href="{{ route('admin.products.show', $productGroup) }}" class="btn btn-secondary">
                👁️ View Product
            </a>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline">
                ← Back to Products
            </a>
        </div>
    </div>
</div>

<!-- Product Info -->
<div class="product-info-card">
    <div class="product-summary">
        <div class="product-image">
            @if($productGroup->main_image)
                <img src="{{ $productGroup->main_image }}" alt="{{ $productGroup->name }}">
            @else
                <div class="no-image">📦</div>
            @endif
        </div>
        <div class="product-details">
            <h3>{{ $productGroup->name }}</h3>
            <p>{{ Str::limit($productGroup->description, 100) }}</p>
            <div class="product-meta">
                <span class="meta-item">Base Price: ${{ number_format($productGroup->base_price, 2) }}</span>
                <span class="meta-item">Category: {{ $productGroup->category->name }}</span>
                <span class="meta-item">{{ $variants->total() }} variants</span>
            </div>
        </div>
    </div>
</div>

<!-- Variants Table -->
<div class="table-card">
    <div class="table-header">
        <h3>Variants ({{ $variants->total() }})</h3>
        <div class="table-actions">
            @if($admin->hasPermission('manage_products'))
                <button onclick="showBulkStockModal()" class="btn btn-outline btn-sm">
                    📦 Bulk Update Stock
                </button>
            @endif
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Variant</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>SKU</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($variants as $variant)
                    <tr>
                        <td>
                            <div class="variant-info">
                                <div class="variant-image">
                                    @if($variant->image)
                                        <img src="{{ $variant->image }}" alt="{{ $variant->color }} {{ $variant->size }}">
                                    @else
                                        <div class="color-swatch" style="background-color: {{ strtolower($variant->color) }}"></div>
                                    @endif
                                </div>
                                <div class="variant-details">
                                    <h4>{{ $variant->color }} - {{ $variant->size }}</h4>
                                    <small>ID: {{ $variant->id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="price-info">
                                <span class="final-price">${{ number_format($productGroup->base_price + $variant->price_adjustment, 2) }}</span>
                                @if($variant->price_adjustment != 0)
                                    <small class="price-adjustment">
                                        ({{ $variant->price_adjustment >= 0 ? '+' : '' }}${{ number_format($variant->price_adjustment, 2) }})
                                    </small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <div class="stock-info">
                                <span class="stock-number {{ $variant->quantity <= 5 ? 'low-stock' : '' }}">
                                    {{ $variant->quantity }}
                                </span>
                                <small class="stock-label">in stock</small>
                            </div>
                        </td>
                        <td>
                            <div class="sku-info">
                                {{ $variant->sku ?: 'No SKU' }}
                            </div>
                        </td>
                        <td>
                            <span class="status-badge {{ $variant->is_available ? 'available' : 'unavailable' }}">
                                {{ $variant->is_available ? 'Available' : 'Unavailable' }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                @if($admin->hasPermission('manage_products'))
                                    <a href="{{ route('admin.products.variants.edit', [$productGroup, $variant]) }}" 
                                       class="btn btn-sm btn-primary" title="Edit">✏️</a>
                                    
                                    <form action="{{ route('admin.products.variants.toggle-availability', [$productGroup, $variant]) }}" 
                                          method="POST" style="display: inline;">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-sm btn-warning" 
                                                title="{{ $variant->is_available ? 'Make Unavailable' : 'Make Available' }}">
                                            {{ $variant->is_available ? '⏸️' : '▶️' }}
                                        </button>
                                    </form>
                                @endif
                                
                                @if($admin->hasPermission('delete_data'))
                                    <form action="{{ route('admin.products.variants.destroy', [$productGroup, $variant]) }}" 
                                          method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this variant?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">🗑️</button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="no-data">
                            <div class="empty-state">
                                <div class="empty-icon">🎨</div>
                                <h3>No variants found</h3>
                                <p>This product doesn't have any variants yet.</p>
                                @if($admin->hasPermission('manage_products'))
                                    <a href="{{ route('admin.products.variants.create', $productGroup) }}" class="btn btn-primary">
                                        ➕ Create First Variant
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($variants->hasPages())
        <div class="pagination-wrapper">
            {{ $variants->links() }}
        </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon">🎨</div>
        <div class="stat-info">
            <div class="stat-number">{{ $variants->total() }}</div>
            <div class="stat-label">Total Variants</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
            <div class="stat-number">{{ $variants->where('is_available', true)->count() }}</div>
            <div class="stat-label">Available</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
            <div class="stat-number">{{ $variants->sum('quantity') }}</div>
            <div class="stat-label">Total Stock</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-info">
            <div class="stat-number">{{ $variants->where('quantity', '<=', 5)->count() }}</div>
            <div class="stat-label">Low Stock</div>
        </div>
    </div>
</div>

<!-- Bulk Stock Update Modal -->
<div id="bulkStockModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📦 Bulk Update Stock</h3>
            <button onclick="hideBulkStockModal()" class="close-btn">✕</button>
        </div>
        <form action="{{ route('admin.products.variants.bulk-update-stock', $productGroup) }}" method="POST">
            @csrf
            @method('PATCH')
            <div class="modal-body">
                <p>Update stock quantities for multiple variants at once:</p>
                <div class="bulk-stock-list">
                    @foreach($variants as $variant)
                        <div class="bulk-stock-item">
                            <label>{{ $variant->color }} - {{ $variant->size }}</label>
                            <input type="hidden" name="variants[{{ $loop->index }}][id]" value="{{ $variant->id }}">
                            <input type="number" name="variants[{{ $loop->index }}][quantity]" 
                                   value="{{ $variant->quantity }}" min="0" class="form-control">
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">Update Stock</button>
                <button type="button" onclick="hideBulkStockModal()" class="btn btn-outline">Cancel</button>
            </div>
        </form>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .product-info-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .product-summary {
        display: flex;
        gap: 1.5rem;
        align-items: center;
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        overflow: hidden;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-image {
        font-size: 2rem;
        color: #ccc;
    }

    .product-details h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }

    .product-details p {
        color: #666;
        margin-bottom: 1rem;
    }

    .product-meta {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .meta-item {
        background: #f8f9fa;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        color: #666;
    }

    .variant-info {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .variant-image {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        overflow: hidden;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .variant-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .color-swatch {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        border: 2px solid #ddd;
    }

    .variant-details h4 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #333;
    }

    .variant-details small {
        color: #666;
        font-size: 0.8rem;
    }

    .price-info {
        display: flex;
        flex-direction: column;
    }

    .final-price {
        font-weight: 600;
        font-size: 1.1rem;
        color: #28a745;
    }

    .price-adjustment {
        color: #666;
        font-size: 0.8rem;
    }

    .stock-info {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .stock-number {
        font-weight: 600;
        font-size: 1.25rem;
        color: #333;
    }

    .stock-number.low-stock {
        color: #dc3545;
    }

    .stock-label {
        color: #666;
        font-size: 0.8rem;
    }

    .sku-info {
        font-family: monospace;
        font-size: 0.9rem;
        color: #666;
    }

    .status-badge.available {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.unavailable {
        background: #f8d7da;
        color: #721c24;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .bulk-stock-list {
        display: grid;
        gap: 1rem;
        max-height: 400px;
        overflow-y: auto;
    }

    .bulk-stock-item {
        display: grid;
        grid-template-columns: 1fr 100px;
        gap: 1rem;
        align-items: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .bulk-stock-item label {
        font-weight: 500;
        color: #333;
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
        }

        .product-summary {
            flex-direction: column;
            text-align: center;
        }

        .product-meta {
            justify-content: center;
        }

        .bulk-stock-item {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function showBulkStockModal() {
        document.getElementById('bulkStockModal').style.display = 'flex';
    }
    
    function hideBulkStockModal() {
        document.getElementById('bulkStockModal').style.display = 'none';
    }
    
    // Close modal when clicking outside
    document.getElementById('bulkStockModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideBulkStockModal();
        }
    });
</script>
@endpush
@endsection
