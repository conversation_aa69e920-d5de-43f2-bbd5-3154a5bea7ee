<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display a listing of categories.
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to category management.');
        }

        $query = Category::withCount('productGroups');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $categories = $query->latest()->paginate(20);

        return view('admin.categories.index', compact('categories', 'admin'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to create categories.');
        }

        return view('admin.categories.create', compact('admin'));
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to create categories.');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:categories,name',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        Category::create([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category created successfully!');
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to view categories.');
        }

        $category->load(['productGroups' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.categories.show', compact('category', 'admin'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(Category $category)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to edit categories.');
        }

        return view('admin.categories.edit', compact('category', 'admin'));
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, Category $category)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to update categories.');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $category->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Category updated successfully!');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy(Category $category)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            abort(403, 'Unauthorized access to delete categories.');
        }

        // Check if category has products
        if ($category->productGroups()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Cannot delete category that has products. Please move or delete the products first.');
        }

        $categoryName = $category->name;
        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', "Category '{$categoryName}' deleted successfully!");
    }

    /**
     * Toggle category status.
     */
    public function toggleStatus(Category $category)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_categories')) {
            abort(403, 'Unauthorized access to modify categories.');
        }

        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        
        return redirect()->back()
            ->with('success', "Category '{$category->name}' has been {$status}!");
    }

    /**
     * Get category statistics.
     */
    public function stats()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('view_analytics')) {
            abort(403, 'Unauthorized access to category analytics.');
        }

        $stats = [
            'total_categories' => Category::count(),
            'active_categories' => Category::where('is_active', true)->count(),
            'categories_with_products' => Category::has('productGroups')->count(),
            'empty_categories' => Category::doesntHave('productGroups')->count(),
        ];

        $categoryProductCounts = Category::withCount('productGroups')
            ->orderBy('product_groups_count', 'desc')
            ->take(10)
            ->get();

        return view('admin.categories.stats', compact('stats', 'categoryProductCounts', 'admin'));
    }
}
