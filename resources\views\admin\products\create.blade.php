@extends('admin.layouts.app')

@section('title', 'Create Product')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">➕ Create New Product</h1>
            <p class="page-subtitle">Add a new product group to your catalog</p>
        </div>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline">
            ← Back to Products
        </a>
    </div>
</div>

<div class="form-card">
    <form action="{{ route('admin.products.store') }}" method="POST" class="product-form">
        @csrf
        
        <div class="form-section">
            <h3 class="section-title">📝 Basic Information</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="name" class="form-label">Product Name *</label>
                    <input type="text" id="name" name="name" class="form-control @error('name') is-invalid @enderror" 
                           value="{{ old('name') }}" required placeholder="Enter product name">
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label for="category_id" class="form-label">Category *</label>
                    <select id="category_id" name="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
                        <option value="">Select a category</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('category_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description *</label>
                <textarea id="description" name="description" rows="4" 
                          class="form-control @error('description') is-invalid @enderror" 
                          required placeholder="Enter detailed product description">{{ old('description') }}</textarea>
                @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">💰 Pricing & Details</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="base_price" class="form-label">Base Price ($) *</label>
                    <input type="number" id="base_price" name="base_price" step="0.01" min="0"
                           class="form-control @error('base_price') is-invalid @enderror" 
                           value="{{ old('base_price') }}" required placeholder="0.00">
                    @error('base_price')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="form-text">This is the base price. Variants can have price adjustments.</small>
                </div>
                
                <div class="form-group">
                    <label for="rating" class="form-label">Initial Rating</label>
                    <select id="rating" name="rating" class="form-control @error('rating') is-invalid @enderror">
                        <option value="">No rating</option>
                        <option value="1" {{ old('rating') == '1' ? 'selected' : '' }}>⭐ 1 Star</option>
                        <option value="2" {{ old('rating') == '2' ? 'selected' : '' }}>⭐⭐ 2 Stars</option>
                        <option value="3" {{ old('rating') == '3' ? 'selected' : '' }}>⭐⭐⭐ 3 Stars</option>
                        <option value="4" {{ old('rating') == '4' ? 'selected' : '' }}>⭐⭐⭐⭐ 4 Stars</option>
                        <option value="5" {{ old('rating') == '5' ? 'selected' : '' }}>⭐⭐⭐⭐⭐ 5 Stars</option>
                    </select>
                    @error('rating')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <div class="form-group">
                <label for="main_image" class="form-label">Main Product Image</label>

                <!-- Image Upload Area -->
                <div class="image-upload-area" id="imageUploadArea">
                    <div class="upload-placeholder" id="uploadPlaceholder">
                        <div class="upload-icon">📷</div>
                        <p>Click to upload image or drag & drop</p>
                        <small>Supports: JPEG, PNG, GIF, WebP (Max: 5MB)</small>
                    </div>
                    <div class="image-preview" id="imagePreview" style="display: none;">
                        <img id="previewImg" src="" alt="Preview">
                        <div class="image-actions">
                            <button type="button" onclick="removeImage()" class="remove-btn">✕</button>
                        </div>
                    </div>
                </div>

                <!-- Hidden file input -->
                <input type="file" id="imageFile" accept="image/*" style="display: none;">

                <!-- Hidden URL input (will be populated after upload) -->
                <input type="hidden" id="main_image" name="main_image" value="{{ old('main_image') }}">

                <!-- Alternative URL input -->
                <div class="url-input-section">
                    <label class="form-label">Or enter image URL:</label>
                    <input type="url" id="image_url" class="form-control"
                           placeholder="https://example.com/image.jpg">
                    <button type="button" onclick="loadImageFromUrl()" class="btn btn-outline btn-sm">Load Image</button>
                </div>

                @error('main_image')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Upload an image file or enter a URL to the main product image.</small>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">🏷️ Tags & Categories</h3>
            
            <div class="form-group">
                <label class="form-label">Product Tags</label>
                <div class="tags-grid">
                    @foreach($tags as $tag)
                        <label class="tag-checkbox">
                            <input type="checkbox" name="tags[]" value="{{ $tag->id }}" 
                                   {{ in_array($tag->id, old('tags', [])) ? 'checked' : '' }}>
                            <span class="tag-label">{{ $tag->name }}</span>
                        </label>
                    @endforeach
                </div>
                @error('tags')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Select relevant tags for this product.</small>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">⚙️ Status & Settings</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <span class="checkmark"></span>
                        Active Product
                    </label>
                    <small class="form-text">Active products are visible to customers.</small>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_trending" value="1" 
                               {{ old('is_trending') ? 'checked' : '' }}>
                        <span class="checkmark"></span>
                        Trending Product
                    </label>
                    <small class="form-text">Trending products are featured prominently.</small>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                ✅ Create Product
            </button>
            <a href="{{ route('admin.products.index') }}" class="btn btn-outline">
                Cancel
            </a>
        </div>
    </form>
</div>

@push('styles')
<style>
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }

    .product-form {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e9ecef;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.875rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-control.is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: #666;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .tags-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.5rem;
    }

    .tag-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .tag-checkbox:hover {
        background: #f8f9fa;
        border-color: #667eea;
    }

    .tag-checkbox input[type="checkbox"] {
        margin: 0;
    }

    .tag-label {
        font-size: 0.9rem;
        color: #333;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        font-weight: 500;
        color: #333;
    }

    .checkbox-label input[type="checkbox"] {
        width: 1.2rem;
        height: 1.2rem;
        margin: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }

    /* Image Upload Styles */
    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .image-upload-area:hover {
        border-color: #667eea;
        background: #f8f9fa;
    }

    .image-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .upload-placeholder {
        color: #666;
    }

    .upload-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .image-preview {
        position: relative;
        display: inline-block;
    }

    .image-preview img {
        max-width: 300px;
        max-height: 200px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .image-actions {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .remove-btn {
        background: rgba(220, 53, 69, 0.9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        cursor: pointer;
        font-size: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .remove-btn:hover {
        background: #dc3545;
    }

    .url-input-section {
        display: flex;
        gap: 0.5rem;
        align-items: end;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .url-input-section .form-control {
        flex: 1;
    }

    .url-input-section .form-label {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
        color: #666;
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }

        .tags-grid {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .url-input-section {
            flex-direction: column;
        }

        .image-preview img {
            max-width: 100%;
        }
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('imageUploadArea');
    const fileInput = document.getElementById('imageFile');
    const placeholder = document.getElementById('uploadPlaceholder');
    const preview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const hiddenInput = document.getElementById('main_image');

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            uploadImage(file);
        }
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            uploadImage(files[0]);
        }
    });

    // Upload image function
    function uploadImage(file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select an image file.');
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }

        // Show loading
        placeholder.innerHTML = '<div class="upload-icon">⏳</div><p>Uploading...</p>';

        // Create FormData
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'product');

        // Upload via AJAX
        fetch('/admin/images/upload', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show preview
                previewImg.src = data.url;
                hiddenInput.value = data.url;
                placeholder.style.display = 'none';
                preview.style.display = 'block';
            } else {
                alert('Upload failed: ' + (data.error || 'Unknown error'));
                resetUploadArea();
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            alert('Upload failed. Please try again.');
            resetUploadArea();
        });
    }

    // Reset upload area
    function resetUploadArea() {
        placeholder.innerHTML = '<div class="upload-icon">📷</div><p>Click to upload image or drag & drop</p><small>Supports: JPEG, PNG, GIF, WebP (Max: 5MB)</small>';
        placeholder.style.display = 'block';
        preview.style.display = 'none';
        hiddenInput.value = '';
        fileInput.value = '';
    }

    // Remove image function
    window.removeImage = function() {
        resetUploadArea();
    };

    // Load image from URL
    window.loadImageFromUrl = function() {
        const url = document.getElementById('image_url').value.trim();
        if (url) {
            previewImg.src = url;
            hiddenInput.value = url;
            placeholder.style.display = 'none';
            preview.style.display = 'block';
            document.getElementById('image_url').value = '';
        }
    };
});
</script>
@endpush
@endsection
