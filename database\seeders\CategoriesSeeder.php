<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategoriesSeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Men\'s Clothing',
                'description' => 'Stylish clothing and accessories for men',
                'is_active' => true
            ],
            [
                'name' => 'Women\'s Clothing',
                'description' => 'Fashion-forward clothing and accessories for women',
                'is_active' => true
            ],
            [
                'name' => 'Kids & Baby',
                'description' => 'Comfortable and cute clothing for children and babies',
                'is_active' => true
            ],
            [
                'name' => 'Sports & Activewear',
                'description' => 'Athletic wear and sports equipment for active lifestyles',
                'is_active' => true
            ],
            [
                'name' => 'Shoes & Footwear',
                'description' => 'Comfortable and stylish shoes for all occasions',
                'is_active' => true
            ],
            [
                'name' => 'Accessories',
                'description' => 'Bags, jewelry, watches, and other fashion accessories',
                'is_active' => true
            ],
            [
                'name' => 'Electronics',
                'description' => 'Latest gadgets, phones, and electronic devices',
                'is_active' => true
            ],
            [
                'name' => 'Home & Garden',
                'description' => 'Home decor, furniture, and gardening supplies',
                'is_active' => true
            ],
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }

        $this->command->info('Categories created successfully!');
    }
}
