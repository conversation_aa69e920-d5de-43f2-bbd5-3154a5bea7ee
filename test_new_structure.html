<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Test New Product Structure</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .test-section {
                margin-bottom: 30px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background: #f9f9f9;
            }
            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                margin: 5px;
                font-size: 14px;
            }
            .btn-primary {
                background: #007bff;
                color: white;
            }
            .btn-success {
                background: #28a745;
                color: white;
            }
            .btn-info {
                background: #17a2b8;
                color: white;
            }
            .btn:hover {
                opacity: 0.9;
            }
            .result {
                margin-top: 15px;
                padding: 10px;
                border-radius: 5px;
                white-space: pre-wrap;
                font-family: monospace;
                font-size: 12px;
                max-height: 300px;
                overflow-y: auto;
            }
            .success {
                background: #d4edda;
                color: #155724;
            }
            .error {
                background: #f8d7da;
                color: #721c24;
            }
            .info {
                background: #d1ecf1;
                color: #0c5460;
            }
            .form-group {
                margin-bottom: 15px;
            }
            .form-group label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            .form-group input,
            .form-group textarea,
            .form-group select {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Test New Simplified Product Structure</h1>

            <div class="test-section">
                <h3>1. Test Form Data Loading</h3>
                <button class="btn btn-info" onclick="testFormData()">
                    Load Form Data
                </button>
                <div id="formDataResult" class="result"></div>
            </div>

            <div class="test-section">
                <h3>2. Create Sample Category</h3>
                <button
                    class="btn btn-primary"
                    onclick="createSampleCategory()"
                >
                    Create Category
                </button>
                <div id="categoryResult" class="result"></div>
            </div>

            <div class="test-section">
                <h3>3. Create Sample Tags</h3>
                <button class="btn btn-primary" onclick="createSampleTags()">
                    Create Tags
                </button>
                <div id="tagsResult" class="result"></div>
            </div>

            <div class="test-section">
                <h3>4. Create Product Group with Variants</h3>
                <div class="form-group">
                    <label>Product Name:</label>
                    <input
                        type="text"
                        id="productName"
                        value="Cotton T-Shirt"
                        placeholder="Product name"
                    />
                </div>
                <div class="form-group">
                    <label>Description:</label>
                    <textarea
                        id="productDescription"
                        placeholder="Product description"
                    >
High quality cotton t-shirt perfect for casual wear</textarea
                    >
                </div>
                <div class="form-group">
                    <label>Base Price ($):</label>
                    <input
                        type="number"
                        id="basePrice"
                        value="29.99"
                        step="0.01"
                        placeholder="Base price"
                    />
                </div>
                <button class="btn btn-success" onclick="createProductGroup()">
                    Create Product Group
                </button>
                <div id="productGroupResult" class="result"></div>
            </div>

            <div class="test-section">
                <h3>5. Test Product Groups API</h3>
                <button class="btn btn-info" onclick="testProductGroups()">
                    Get Product Groups
                </button>
                <button class="btn btn-info" onclick="testHomepage()">
                    Get Homepage Products
                </button>
                <div id="productGroupsResult" class="result"></div>
            </div>
        </div>

        <script>
            const API_BASE = "http://localhost:8000/api";
            let createdCategoryId = null;
            let createdTagIds = [];
            let createdProductGroupId = null;

            async function makeRequest(url, method = "GET", data = null) {
                try {
                    const options = {
                        method: method,
                        headers: {
                            "Content-Type": "application/json",
                            Accept: "application/json",
                        },
                    };

                    if (data) {
                        options.body = JSON.stringify(data);
                    }

                    const response = await fetch(url, options);
                    const result = await response.json();

                    return {
                        success: response.ok,
                        status: response.status,
                        data: result,
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message,
                    };
                }
            }

            function displayResult(elementId, result) {
                const element = document.getElementById(elementId);

                if (result.success) {
                    element.className = "result success";
                    element.textContent = `✅ Success (${
                        result.status
                    })\n\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    element.className = "result error";
                    element.textContent = `❌ Failed\n\n${
                        result.error || JSON.stringify(result.data, null, 2)
                    }`;
                }
            }

            async function testFormData() {
                const result = await makeRequest(
                    `${API_BASE}/product-groups/form-data`
                );
                displayResult("formDataResult", result);
            }

            async function createSampleCategory() {
                const data = {
                    name: "Clothing " + Date.now(),
                    description: "Sample clothing category",
                };

                const result = await makeRequest(
                    `${API_BASE}/categories`,
                    "POST",
                    data
                );
                if (result.success) {
                    createdCategoryId = result.data.id;
                }
                displayResult("categoryResult", result);
            }

            async function createSampleTags() {
                const tags = ["Cotton", "Casual", "Summer"];
                const results = [];

                for (const tagName of tags) {
                    const data = { name: tagName + " " + Date.now() };
                    const result = await makeRequest(
                        `${API_BASE}/tags`,
                        "POST",
                        data
                    );
                    if (result.success) {
                        createdTagIds.push(result.data.id);
                    }
                    results.push(result);
                }

                displayResult("tagsResult", { success: true, data: results });
            }

            async function createProductGroup() {
                if (!createdCategoryId) {
                    alert("Please create a category first");
                    return;
                }

                const data = {
                    name: document.getElementById("productName").value,
                    description:
                        document.getElementById("productDescription").value,
                    category_id: createdCategoryId,
                    base_price: parseFloat(
                        document.getElementById("basePrice").value
                    ),
                    is_active: true,
                    is_trending: false,
                    rating: 4.5,
                    tags: createdTagIds,
                    variants: [
                        {
                            color: "Red",
                            size: "S",
                            price_adjustment: 0,
                            quantity: 10,
                            image: null,
                        },
                        {
                            color: "Red",
                            size: "M",
                            price_adjustment: 0,
                            quantity: 15,
                            image: null,
                        },
                        {
                            color: "Blue",
                            size: "S",
                            price_adjustment: 2.0,
                            quantity: 8,
                            image: null,
                        },
                        {
                            color: "Blue",
                            size: "M",
                            price_adjustment: 2.0,
                            quantity: 12,
                            image: null,
                        },
                    ],
                };

                const result = await makeRequest(
                    `${API_BASE}/product-groups`,
                    "POST",
                    data
                );
                if (result.success) {
                    createdProductGroupId = result.data.product_group.id;
                }
                displayResult("productGroupResult", result);
            }

            async function testProductGroups() {
                const result = await makeRequest(`${API_BASE}/product-groups`);
                displayResult("productGroupsResult", result);
            }

            async function testHomepage() {
                const result = await makeRequest(
                    `${API_BASE}/product-groups/homepage?per_page=5`
                );
                displayResult("productGroupsResult", result);
            }

            // Auto-load form data on page load
            window.onload = () => {
                console.log("Testing page loaded");
            };
        </script>
    </body>
</html>
