<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إنشاء المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.9; }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار API إنشاء المنتجات</h1>
        
        <div class="test-section">
            <h3>1. اختبار تحميل بيانات النموذج</h3>
            <button class="btn btn-info" onclick="testFormData()">تحميل البيانات</button>
            <div id="formDataResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. اختبار إضافة فئة جديدة</h3>
            <button class="btn btn-primary" onclick="testAddCategory()">إضافة فئة</button>
            <div id="categoryResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. اختبار إضافة لون جديد</h3>
            <button class="btn btn-primary" onclick="testAddColor()">إضافة لون</button>
            <div id="colorResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>4. اختبار إضافة حجم جديد</h3>
            <button class="btn btn-primary" onclick="testAddSize()">إضافة حجم</button>
            <div id="sizeResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. اختبار إضافة تاغ جديد</h3>
            <button class="btn btn-primary" onclick="testAddTag()">إضافة تاغ</button>
            <div id="tagResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. اختبار إنشاء منتج كامل</h3>
            <button class="btn btn-success" onclick="testCreateCompleteProduct()">إنشاء منتج كامل</button>
            <div id="productResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>7. اختبار عرض المنتجات</h3>
            <button class="btn btn-info" onclick="testGetProducts()">عرض المنتجات</button>
            <div id="productsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(url, options);
                const result = await response.json();

                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = `✅ نجح الطلب (${result.status})\n\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ فشل الطلب\n\n${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        async function testFormData() {
            const result = await makeRequest(`${API_BASE}/product-management/form-data`);
            displayResult('formDataResult', result);
        }

        async function testAddCategory() {
            const data = {
                type: 'category',
                name: 'فئة تجريبية ' + Date.now(),
                extra_data: {
                    description: 'وصف الفئة التجريبية'
                }
            };
            
            const result = await makeRequest(`${API_BASE}/product-management/quick-add`, 'POST', data);
            displayResult('categoryResult', result);
        }

        async function testAddColor() {
            const data = {
                type: 'color',
                name: 'لون تجريبي ' + Date.now(),
                extra_data: {
                    hex_code: '#' + Math.floor(Math.random()*16777215).toString(16)
                }
            };
            
            const result = await makeRequest(`${API_BASE}/product-management/quick-add`, 'POST', data);
            displayResult('colorResult', result);
        }

        async function testAddSize() {
            const sizes = ['XS', 'S', 'M', 'L', 'XL'];
            const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
            
            const data = {
                type: 'size',
                name: 'حجم تجريبي ' + Date.now(),
                extra_data: {
                    abbreviation: randomSize
                }
            };
            
            const result = await makeRequest(`${API_BASE}/product-management/quick-add`, 'POST', data);
            displayResult('sizeResult', result);
        }

        async function testAddTag() {
            const data = {
                type: 'tag',
                name: 'تاغ تجريبي ' + Date.now()
            };
            
            const result = await makeRequest(`${API_BASE}/product-management/quick-add`, 'POST', data);
            displayResult('tagResult', result);
        }

        async function testCreateCompleteProduct() {
            const timestamp = Date.now();
            
            const data = {
                product: {
                    name: `منتج تجريبي ${timestamp}`,
                    description: 'وصف المنتج التجريبي',
                    price: 99.99,
                    is_active: true,
                    is_trending: false,
                    rating: 4.5
                },
                category: {
                    name: `فئة المنتج ${timestamp}`,
                    description: 'وصف الفئة'
                },
                colors: [
                    {
                        name: `أحمر ${timestamp}`,
                        hex_code: '#FF0000'
                    },
                    {
                        name: `أزرق ${timestamp}`,
                        hex_code: '#0000FF'
                    }
                ],
                sizes: [
                    {
                        name: `صغير ${timestamp}`,
                        abbreviation: 'S'
                    },
                    {
                        name: `متوسط ${timestamp}`,
                        abbreviation: 'M'
                    }
                ],
                tags: [
                    {
                        name: `تاغ1 ${timestamp}`
                    },
                    {
                        name: `تاغ2 ${timestamp}`
                    }
                ],
                variants: [
                    {
                        color_index: 0,
                        size_index: 0,
                        quantity: 10,
                        image: null
                    },
                    {
                        color_index: 0,
                        size_index: 1,
                        quantity: 15,
                        image: null
                    },
                    {
                        color_index: 1,
                        size_index: 0,
                        quantity: 8,
                        image: null
                    },
                    {
                        color_index: 1,
                        size_index: 1,
                        quantity: 12,
                        image: null
                    }
                ]
            };
            
            const result = await makeRequest(`${API_BASE}/product-management/create-complete`, 'POST', data);
            displayResult('productResult', result);
        }

        async function testGetProducts() {
            const result = await makeRequest(`${API_BASE}/products/homepage?per_page=5`);
            displayResult('productsResult', result);
        }

        // تشغيل اختبار تلقائي عند تحميل الصفحة
        window.onload = () => {
            console.log('صفحة الاختبار جاهزة');
        };
    </script>
</body>
</html>
