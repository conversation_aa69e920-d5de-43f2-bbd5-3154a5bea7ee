<?php

// Quick test to check if admin.dashboard route exists
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

try {
    $route = route('admin.dashboard');
    echo "✅ Route 'admin.dashboard' exists: " . $route . "\n";
} catch (Exception $e) {
    echo "❌ Route 'admin.dashboard' not found: " . $e->getMessage() . "\n";
}

// List all admin routes
$routes = Route::getRoutes();
echo "\n📋 All admin routes:\n";
foreach ($routes as $route) {
    if (str_contains($route->getName() ?? '', 'admin.')) {
        echo "- " . $route->getName() . " => " . $route->uri() . "\n";
    }
}
