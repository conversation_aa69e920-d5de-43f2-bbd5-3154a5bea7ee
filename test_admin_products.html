<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Product Management Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-section h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .test-section p {
            margin-bottom: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            background: #f8f9fa;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card h4 {
            margin-bottom: 1rem;
            color: #fff;
            font-size: 1.2rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }

        .workflow-steps {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .workflow-steps ol {
            padding-left: 1.5rem;
        }

        .workflow-steps li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }

        .admin-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .account-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .account-card h5 {
            margin-bottom: 0.5rem;
            color: #fff;
        }

        .account-card p {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .permissions-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: inline-block;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }

            .features-grid, .admin-accounts {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📦 Admin Product Management System</h1>
            <p class="subtitle">Complete CRUD Operations for Product Catalog Management</p>
        </div>

        <div class="test-section">
            <h3>🚀 Quick Access</h3>
            <p>Access the new admin product management system with full CRUD operations.</p>
            <a href="http://localhost:8000/admin/login" class="btn btn-primary">Admin Login</a>
            <a href="http://localhost:8000/admin/dashboard" class="btn">Admin Dashboard</a>
            <a href="http://localhost:8000/admin/products" class="btn">Product Management</a>
        </div>

        <div class="test-section">
            <h3>👨‍💼 Test Admin Accounts</h3>
            <p>Use these accounts to test different permission levels:</p>
            
            <div class="admin-accounts">
                <div class="account-card">
                    <h5>🔴 Super Admin</h5>
                    <p><strong><EMAIL></strong></p>
                    <p>password123</p>
                    <span class="permissions-badge">Full Access</span>
                </div>

                <div class="account-card">
                    <h5>🔵 Store Manager</h5>
                    <p><strong><EMAIL></strong></p>
                    <p>password123</p>
                    <span class="permissions-badge">Product Management</span>
                </div>

                <div class="account-card">
                    <h5>🟢 Moderator</h5>
                    <p><strong><EMAIL></strong></p>
                    <p>password123</p>
                    <span class="permissions-badge">Limited Access</span>
                </div>

                <div class="account-card">
                    <h5>🟡 Editor</h5>
                    <p><strong><EMAIL></strong></p>
                    <p>password123</p>
                    <span class="permissions-badge">Basic Editing</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>✨ Product Management Features</h3>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h4>📋 Product Groups</h4>
                    <ul class="feature-list">
                        <li>Create, edit, delete product groups</li>
                        <li>Category assignment</li>
                        <li>Tag management</li>
                        <li>Price and rating settings</li>
                        <li>Status management (active/inactive)</li>
                        <li>Trending product marking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🎨 Product Variants</h4>
                    <ul class="feature-list">
                        <li>Color and size variants</li>
                        <li>Individual pricing adjustments</li>
                        <li>Stock quantity management</li>
                        <li>SKU generation</li>
                        <li>Availability toggle</li>
                        <li>Bulk stock updates</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔍 Advanced Features</h4>
                    <ul class="feature-list">
                        <li>Search and filtering</li>
                        <li>Category-based filtering</li>
                        <li>Status filtering</li>
                        <li>Pagination</li>
                        <li>Bulk operations</li>
                        <li>Permission-based access</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>📊 Analytics & Reports</h4>
                    <ul class="feature-list">
                        <li>Product statistics</li>
                        <li>Stock level monitoring</li>
                        <li>Category distribution</li>
                        <li>Trending products tracking</li>
                        <li>Variant performance</li>
                        <li>Export capabilities</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Testing Workflow</h3>
            <p>Follow this comprehensive workflow to test all product management features:</p>
            
            <div class="workflow-steps">
                <ol>
                    <li><strong>Login as Super Admin</strong> → <EMAIL> / password123</li>
                    <li><strong>Access Product Management</strong> → Navigate to Products section</li>
                    <li><strong>Create New Product</strong> → Add product group with details</li>
                    <li><strong>Add Variants</strong> → Create color/size combinations</li>
                    <li><strong>Test Filtering</strong> → Use search and category filters</li>
                    <li><strong>Manage Stock</strong> → Update quantities and availability</li>
                    <li><strong>Toggle Status</strong> → Activate/deactivate products</li>
                    <li><strong>Test Permissions</strong> → Login as different admin roles</li>
                    <li><strong>Bulk Operations</strong> → Test bulk stock updates</li>
                    <li><strong>Delete Products</strong> → Test deletion with confirmation</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h3>🔐 Permission System</h3>
            <p>The system includes role-based permissions for different admin levels:</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h4>Super Admin</h4>
                    <ul class="feature-list">
                        <li>Full product management</li>
                        <li>Create, edit, delete products</li>
                        <li>Manage variants</li>
                        <li>Delete data permissions</li>
                        <li>Export capabilities</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Manager</h4>
                    <ul class="feature-list">
                        <li>Product management</li>
                        <li>Variant management</li>
                        <li>Stock updates</li>
                        <li>Status changes</li>
                        <li>Limited delete access</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Moderator</h4>
                    <ul class="feature-list">
                        <li>View products</li>
                        <li>Edit existing products</li>
                        <li>Manage variants</li>
                        <li>Stock updates</li>
                        <li>No delete permissions</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>Editor</h4>
                    <ul class="feature-list">
                        <li>View products</li>
                        <li>Basic editing</li>
                        <li>Limited variant access</li>
                        <li>No delete permissions</li>
                        <li>Read-only analytics</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌐 Navigation</h3>
            <a href="http://localhost:8000/" class="btn">Main Website</a>
            <a href="http://localhost:8000/admin/login" class="btn btn-primary">Admin Login</a>
            <a href="http://localhost:8000/admin/dashboard" class="btn">Admin Dashboard</a>
            <a href="http://localhost:8000/test_admin_auth.html" class="btn">Admin Auth Test</a>
            <a href="http://localhost:8000/admin_panel.html" class="btn">Legacy Panel</a>
        </div>
    </div>
</body>
</html>
