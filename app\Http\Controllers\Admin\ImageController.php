<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ImageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Upload image for products.
     */
    public function upload(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'type' => 'required|in:product,variant',
        ]);

        try {
            $image = $request->file('image');
            $type = $request->type;
            
            // Generate unique filename
            $filename = $type . '_' . time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
            
            // Store in public/images/{type} directory
            $path = $image->storeAs("images/{$type}", $filename, 'public');
            
            // Generate full URL
            $url = Storage::url($path);
            
            return response()->json([
                'success' => true,
                'url' => $url,
                'path' => $path,
                'filename' => $filename,
                'size' => $image->getSize(),
                'mime_type' => $image->getMimeType(),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to upload image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete uploaded image.
     */
    public function delete(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'path' => 'required|string',
        ]);

        try {
            $path = $request->path;
            
            // Remove /storage/ prefix if present
            $path = str_replace('/storage/', '', $path);
            
            if (Storage::disk('public')->exists($path)) {
                Storage::disk('public')->delete($path);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Image deleted successfully'
                ]);
            } else {
                return response()->json([
                    'error' => 'Image not found'
                ], 404);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all uploaded images.
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $type = $request->get('type', 'product');
        $directory = "images/{$type}";
        
        try {
            $files = Storage::disk('public')->files($directory);
            $images = [];
            
            foreach ($files as $file) {
                $images[] = [
                    'path' => $file,
                    'url' => Storage::url($file),
                    'filename' => basename($file),
                    'size' => Storage::disk('public')->size($file),
                    'modified' => Storage::disk('public')->lastModified($file),
                ];
            }
            
            // Sort by modification date (newest first)
            usort($images, function($a, $b) {
                return $b['modified'] - $a['modified'];
            });
            
            return response()->json([
                'success' => true,
                'images' => $images,
                'total' => count($images),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load images: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show image gallery for selection.
     */
    public function gallery(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to image gallery.');
        }

        $type = $request->get('type', 'product');
        
        return view('admin.images.gallery', compact('type', 'admin'));
    }

    /**
     * Resize and optimize image.
     */
    public function resize(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $request->validate([
            'path' => 'required|string',
            'width' => 'required|integer|min:50|max:2000',
            'height' => 'required|integer|min:50|max:2000',
        ]);

        try {
            $originalPath = str_replace('/storage/', '', $request->path);
            $width = $request->width;
            $height = $request->height;
            
            if (!Storage::disk('public')->exists($originalPath)) {
                return response()->json(['error' => 'Original image not found'], 404);
            }
            
            // For now, return the original image
            // In production, you'd use an image manipulation library like Intervention Image
            return response()->json([
                'success' => true,
                'url' => $request->path,
                'message' => 'Image resize feature coming soon'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to resize image: ' . $e->getMessage()
            ], 500);
        }
    }
}
