<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication System Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-section h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .test-section p {
            margin-bottom: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            background: #f8f9fa;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔐 Authentication System Test</h1>
            <p class="subtitle">Laravel Blade Authentication with Modern Design</p>
        </div>

        <div class="test-section">
            <h3>🏠 Welcome Page</h3>
            <p>The main landing page with authentication-aware navigation.</p>
            <a href="http://localhost:8000/" class="btn btn-primary">Visit Welcome Page</a>
        </div>

        <div class="test-section">
            <h3>🔑 Authentication Pages</h3>
            <p>Test the login and registration forms with modern responsive design.</p>
            <a href="http://localhost:8000/login" class="btn">Login Page</a>
            <a href="http://localhost:8000/register" class="btn">Register Page</a>
        </div>

        <div class="test-section">
            <h3>📊 Protected Dashboard</h3>
            <p>Access the dashboard (requires authentication). You'll be redirected to login if not authenticated.</p>
            <a href="http://localhost:8000/dashboard" class="btn">Dashboard</a>
        </div>

        <div class="test-section">
            <h3>🛠️ Admin Panel Integration</h3>
            <p>Access the existing admin panel (works with or without authentication).</p>
            <a href="http://localhost:8000/admin_panel.html" class="btn">Admin Panel</a>
        </div>

        <div class="test-section">
            <h3>✨ Features Implemented</h3>
            <ul class="feature-list">
                <li>Modern responsive login and register forms</li>
                <li>Password strength indicator on registration</li>
                <li>Form validation with error messages</li>
                <li>Flash messages for success/error feedback</li>
                <li>Protected routes with middleware</li>
                <li>Dashboard with product statistics</li>
                <li>Beautiful gradient design with glassmorphism</li>
                <li>Mobile-responsive layouts</li>
                <li>Integration with existing admin panel</li>
                <li>Remember me functionality</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 Test Workflow</h3>
            <p>Follow this workflow to test the complete authentication system:</p>
            <div class="code-block">
1. Visit Welcome Page → Should show login/register buttons
2. Click "Get Started" → Register a new account
3. Fill registration form → Should auto-login and redirect to dashboard
4. Logout → Should redirect to login with success message
5. Login again → Should redirect to dashboard
6. Access Admin Panel → Should work seamlessly
7. Try accessing /dashboard without login → Should redirect to login
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Technical Details</h3>
            <p>The authentication system includes:</p>
            <ul class="feature-list">
                <li>Laravel's built-in authentication guards</li>
                <li>Blade templating engine with layouts</li>
                <li>CSRF protection on all forms</li>
                <li>Password hashing with bcrypt</li>
                <li>Session-based authentication</li>
                <li>Middleware protection for routes</li>
                <li>Custom controllers for auth logic</li>
                <li>Responsive CSS with modern design</li>
            </ul>
        </div>
    </div>
</body>
</html>
