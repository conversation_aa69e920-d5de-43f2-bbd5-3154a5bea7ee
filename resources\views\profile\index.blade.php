@extends('layouts.main')

@section('title', 'My Profile - E-Commerce Store')

@section('content')
<div class="container">
    <div class="profile-layout">
        <!-- Profile Sidebar -->
        <aside class="profile-sidebar">
            <div class="profile-card">
                <div class="profile-avatar">
                    <div class="avatar-circle">
                        {{ strtoupper(substr($user->name, 0, 2)) }}
                    </div>
                </div>
                <div class="profile-info">
                    <h3 class="profile-name">{{ $user->name }}</h3>
                    <p class="profile-email">{{ $user->email }}</p>
                    <p class="profile-since">Member since {{ $user->created_at->format('M Y') }}</p>
                </div>
            </div>

            <nav class="profile-nav">
                <a href="{{ route('profile') }}" class="nav-item active">
                    <span class="nav-icon">👤</span>
                    <span class="nav-text">Profile Overview</span>
                </a>
                <a href="{{ route('profile.edit') }}" class="nav-item">
                    <span class="nav-icon">✏️</span>
                    <span class="nav-text">Edit Profile</span>
                </a>
                <a href="{{ route('profile.password') }}" class="nav-item">
                    <span class="nav-icon">🔒</span>
                    <span class="nav-text">Change Password</span>
                </a>
                <a href="{{ url('/orders') }}" class="nav-item">
                    <span class="nav-icon">📦</span>
                    <span class="nav-text">My Orders</span>
                </a>
                <a href="{{ url('/wishlist') }}" class="nav-item">
                    <span class="nav-icon">❤️</span>
                    <span class="nav-text">My Wishlist</span>
                </a>
                <a href="{{ route('profile.settings') }}" class="nav-item">
                    <span class="nav-icon">⚙️</span>
                    <span class="nav-text">Settings</span>
                </a>
            </nav>
        </aside>

        <!-- Profile Content -->
        <main class="profile-content">
            <div class="page-header">
                <h1 class="page-title">👤 Profile Overview</h1>
                <p class="page-subtitle">Manage your account information and preferences</p>
            </div>

            <!-- Account Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📦</div>
                    <div class="stat-info">
                        <div class="stat-number">{{ $stats['total_orders'] }}</div>
                        <div class="stat-label">Total Orders</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                        <div class="stat-number">${{ number_format($stats['total_spent'], 2) }}</div>
                        <div class="stat-label">Total Spent</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">❤️</div>
                    <div class="stat-info">
                        <div class="stat-number">{{ $stats['wishlist_items'] }}</div>
                        <div class="stat-label">Wishlist Items</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📅</div>
                    <div class="stat-info">
                        <div class="stat-number">{{ $stats['account_age'] }}</div>
                        <div class="stat-label">Days as Member</div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="info-section">
                <div class="section-header">
                    <h2 class="section-title">📋 Account Information</h2>
                    <a href="{{ route('profile.edit') }}" class="btn btn-outline btn-sm">✏️ Edit</a>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Full Name:</span>
                        <span class="info-value">{{ $user->name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email Address:</span>
                        <span class="info-value">{{ $user->email }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Phone Number:</span>
                        <span class="info-value">{{ $user->phone ?: 'Not provided' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Address:</span>
                        <span class="info-value">
                            @if($user->address)
                                {{ $user->address }}
                                @if($user->city), {{ $user->city }}@endif
                                @if($user->state), {{ $user->state }}@endif
                                @if($user->zip_code) {{ $user->zip_code }}@endif
                                @if($user->country), {{ $user->country }}@endif
                            @else
                                Not provided
                            @endif
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Account Created:</span>
                        <span class="info-value">{{ $user->created_at->format('M d, Y H:i') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Last Updated:</span>
                        <span class="info-value">{{ $user->updated_at->format('M d, Y H:i') }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="actions-section">
                <h2 class="section-title">⚡ Quick Actions</h2>
                <div class="actions-grid">
                    <a href="{{ route('profile.edit') }}" class="action-card">
                        <div class="action-icon">✏️</div>
                        <div class="action-info">
                            <h3>Edit Profile</h3>
                            <p>Update your personal information and contact details</p>
                        </div>
                    </a>
                    <a href="{{ route('profile.password') }}" class="action-card">
                        <div class="action-icon">🔒</div>
                        <div class="action-info">
                            <h3>Change Password</h3>
                            <p>Update your account password for security</p>
                        </div>
                    </a>
                    <a href="{{ url('/orders') }}" class="action-card">
                        <div class="action-icon">📦</div>
                        <div class="action-info">
                            <h3>View Orders</h3>
                            <p>Check your order history and track shipments</p>
                        </div>
                    </a>
                    <a href="{{ url('/wishlist') }}" class="action-card">
                        <div class="action-icon">❤️</div>
                        <div class="action-info">
                            <h3>My Wishlist</h3>
                            <p>View and manage your saved products</p>
                        </div>
                    </a>
                    <a href="{{ route('profile.settings') }}" class="action-card">
                        <div class="action-icon">⚙️</div>
                        <div class="action-info">
                            <h3>Account Settings</h3>
                            <p>Manage notifications and privacy preferences</p>
                        </div>
                    </a>
                    <a href="{{ url('/products') }}" class="action-card">
                        <div class="action-icon">🛍️</div>
                        <div class="action-info">
                            <h3>Continue Shopping</h3>
                            <p>Browse our latest products and deals</p>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Account Security -->
            <div class="security-section">
                <h2 class="section-title">🔐 Account Security</h2>
                <div class="security-info">
                    <div class="security-item">
                        <div class="security-icon">🔒</div>
                        <div class="security-text">
                            <h4>Password Protection</h4>
                            <p>Your account is protected with a secure password. Last changed: {{ $user->updated_at->format('M d, Y') }}</p>
                        </div>
                        <a href="{{ route('profile.password') }}" class="btn btn-outline btn-sm">Change Password</a>
                    </div>
                    <div class="security-item">
                        <div class="security-icon">📧</div>
                        <div class="security-text">
                            <h4>Email Verification</h4>
                            <p>Your email address is verified and secure.</p>
                        </div>
                        <span class="status-badge verified">✅ Verified</span>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

@push('styles')
<style>
    .profile-layout {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    /* Profile Sidebar */
    .profile-sidebar {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .profile-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .profile-avatar {
        margin-bottom: 1.5rem;
    }

    .avatar-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0 auto;
    }

    .profile-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .profile-email {
        color: #6b7280;
        margin-bottom: 0.5rem;
    }

    .profile-since {
        color: #9ca3af;
        font-size: 0.875rem;
    }

    .profile-nav {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .nav-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        text-decoration: none;
        color: #6b7280;
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
    }

    .nav-item:hover,
    .nav-item.active {
        background: #eff6ff;
        color: #3b82f6;
    }

    .nav-item:last-child {
        margin-bottom: 0;
    }

    .nav-icon {
        font-size: 1.125rem;
    }

    .nav-text {
        font-weight: 500;
    }

    /* Profile Content */
    .profile-content {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .page-header {
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: #6b7280;
        font-size: 1.125rem;
    }

    /* Stats Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .stat-card {
        background: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        border: 1px solid #e5e7eb;
    }

    .stat-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Info Section */
    .info-section,
    .actions-section,
    .security-section {
        margin-bottom: 3rem;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1f2937;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 1rem;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
    }

    .info-label {
        font-weight: 500;
        color: #374151;
    }

    .info-value {
        color: #1f2937;
        text-align: right;
        max-width: 60%;
        word-break: break-word;
    }

    /* Actions Grid */
    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .action-card {
        background: #f8fafc;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #3b82f6;
    }

    .action-icon {
        font-size: 2rem;
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .action-info h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .action-info p {
        color: #6b7280;
        font-size: 0.875rem;
        line-height: 1.4;
    }

    /* Security Section */
    .security-info {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .security-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
    }

    .security-icon {
        font-size: 1.5rem;
        width: 50px;
        height: 50px;
        background: white;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .security-text {
        flex: 1;
    }

    .security-text h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
    }

    .security-text p {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-badge.verified {
        background: #d1fae5;
        color: #065f46;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .profile-layout {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .info-grid {
            grid-template-columns: 1fr;
        }

        .info-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .info-value {
            max-width: 100%;
            text-align: left;
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }

        .security-item {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
@endpush
@endsection
