@extends('admin.layouts.app')

@section('title', 'Tag Management')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">🏷️ Tag Management</h1>
            <p class="page-subtitle">Manage product tags and labels</p>
        </div>
        <div class="header-actions">
            @if($admin->hasPermission('manage_tags'))
                <a href="{{ route('admin.tags.create') }}" class="btn btn-primary">
                    ➕ Add Tag
                </a>
                <button onclick="showBulkCreateModal()" class="btn btn-secondary">
                    📝 Bulk Create
                </button>
            @endif
            @if($admin->hasPermission('view_analytics'))
                <a href="{{ route('admin.tags.stats') }}" class="btn btn-outline">
                    📊 Statistics
                </a>
            @endif
            @if($admin->hasPermission('delete_data'))
                <form action="{{ route('admin.tags.clean-unused') }}" method="POST" style="display: inline;"
                      onsubmit="return confirm('Are you sure you want to delete all unused tags?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-warning">
                        🧹 Clean Unused
                    </button>
                </form>
            @endif
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filters-card">
    <form method="GET" action="{{ route('admin.tags.index') }}" class="filters-form">
        <div class="filter-group">
            <input type="text" name="search" placeholder="Search tags..." 
                   value="{{ request('search') }}" class="filter-input">
        </div>
        
        <button type="submit" class="btn btn-secondary">🔍 Search</button>
        <a href="{{ route('admin.tags.index') }}" class="btn btn-outline">Clear</a>
    </form>
</div>

<!-- Tags Grid -->
<div class="tags-container">
    <div class="table-header">
        <h3>Tags ({{ $tags->total() }})</h3>
        <div class="view-toggle">
            <button onclick="toggleView('grid')" class="view-btn active" id="grid-btn">🔲 Grid</button>
            <button onclick="toggleView('table')" class="view-btn" id="table-btn">📋 Table</button>
        </div>
    </div>
    
    <!-- Grid View -->
    <div id="grid-view" class="tags-grid">
        @forelse($tags as $tag)
            <div class="tag-card">
                <div class="tag-header">
                    <h4 class="tag-name">{{ $tag->name }}</h4>
                    <div class="tag-actions">
                        @if($admin->hasPermission('manage_tags'))
                            <a href="{{ route('admin.tags.edit', $tag) }}" 
                               class="action-btn edit" title="Edit">✏️</a>
                        @endif
                        @if($admin->hasPermission('delete_data'))
                            <form action="{{ route('admin.tags.destroy', $tag) }}" 
                                  method="POST" style="display: inline;"
                                  onsubmit="return confirm('Are you sure you want to delete this tag?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="action-btn delete" title="Delete">🗑️</button>
                            </form>
                        @endif
                    </div>
                </div>
                
                <div class="tag-stats">
                    <div class="stat">
                        <span class="stat-number">{{ $tag->product_groups_count }}</span>
                        <span class="stat-label">products</span>
                    </div>
                </div>
                
                <div class="tag-footer">
                    <small class="tag-date">Created {{ $tag->created_at->diffForHumans() }}</small>
                    @if($tag->product_groups_count > 0)
                        <a href="{{ route('admin.tags.show', $tag) }}" class="view-products">
                            View Products →
                        </a>
                    @endif
                </div>
            </div>
        @empty
            <div class="empty-state">
                <div class="empty-icon">🏷️</div>
                <h3>No tags found</h3>
                <p>Start by creating your first product tag.</p>
                @if($admin->hasPermission('manage_tags'))
                    <a href="{{ route('admin.tags.create') }}" class="btn btn-primary">
                        ➕ Create First Tag
                    </a>
                @endif
            </div>
        @endforelse
    </div>
    
    <!-- Table View (Hidden by default) -->
    <div id="table-view" class="table-responsive" style="display: none;">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Tag Name</th>
                    <th>Products</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($tags as $tag)
                    <tr>
                        <td>
                            <div class="tag-info">
                                <h4>{{ $tag->name }}</h4>
                                <small>ID: {{ $tag->id }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="products-count">
                                <span class="count-number">{{ $tag->product_groups_count }}</span>
                                <small class="count-label">products</small>
                            </div>
                        </td>
                        <td>
                            <div class="date-info">
                                <span class="date">{{ $tag->created_at->format('M d, Y') }}</span>
                                <small class="time">{{ $tag->created_at->diffForHumans() }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ route('admin.tags.show', $tag) }}" 
                                   class="btn btn-sm btn-outline" title="View">👁️</a>
                                
                                @if($admin->hasPermission('manage_tags'))
                                    <a href="{{ route('admin.tags.edit', $tag) }}" 
                                       class="btn btn-sm btn-primary" title="Edit">✏️</a>
                                @endif
                                
                                @if($admin->hasPermission('delete_data'))
                                    <form action="{{ route('admin.tags.destroy', $tag) }}" 
                                          method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this tag?')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">🗑️</button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    
    @if($tags->hasPages())
        <div class="pagination-wrapper">
            {{ $tags->withQueryString()->links() }}
        </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon">🏷️</div>
        <div class="stat-info">
            <div class="stat-number">{{ $tags->total() }}</div>
            <div class="stat-label">Total Tags</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
            <div class="stat-number">{{ $tags->where('product_groups_count', '>', 0)->count() }}</div>
            <div class="stat-label">Used Tags</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">🚫</div>
        <div class="stat-info">
            <div class="stat-number">{{ $tags->where('product_groups_count', 0)->count() }}</div>
            <div class="stat-label">Unused Tags</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-info">
            <div class="stat-number">{{ $tags->sum('product_groups_count') }}</div>
            <div class="stat-label">Total Usage</div>
        </div>
    </div>
</div>

<!-- Bulk Create Modal -->
<div id="bulkCreateModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>📝 Bulk Create Tags</h3>
            <button onclick="hideBulkCreateModal()" class="close-btn">✕</button>
        </div>
        <form action="{{ route('admin.tags.bulk-create') }}" method="POST">
            @csrf
            <div class="modal-body">
                <div class="form-group">
                    <label for="bulk-tags" class="form-label">Tags (comma-separated)</label>
                    <textarea id="bulk-tags" name="tags" rows="6" class="form-control" 
                              placeholder="Enter tags separated by commas, e.g.: Electronics, Gadgets, Mobile, Accessories, Tech"></textarea>
                    <small class="form-text">Enter multiple tags separated by commas. Duplicate tags will be skipped.</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">Create Tags</button>
                <button type="button" onclick="hideBulkCreateModal()" class="btn btn-outline">Cancel</button>
            </div>
        </form>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
        flex-wrap: wrap;
    }

    .tags-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .table-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .view-toggle {
        display: flex;
        gap: 0.5rem;
    }

    .view-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #e9ecef;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .view-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .tags-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .tag-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .tag-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .tag-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .tag-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin: 0;
    }

    .tag-actions {
        display: flex;
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.25rem 0.5rem;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .action-btn.edit:hover {
        background: #667eea;
        color: white;
    }

    .action-btn.delete:hover {
        background: #dc3545;
        color: white;
    }

    .tag-stats {
        text-align: center;
        margin-bottom: 1rem;
    }

    .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: #667eea;
    }

    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }

    .tag-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
    }

    .tag-date {
        color: #666;
        font-size: 0.8rem;
    }

    .view-products {
        color: #667eea;
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .view-products:hover {
        text-decoration: underline;
    }

    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 12px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
    }

    .modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        color: #333;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1.5rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
        }

        .tags-grid {
            grid-template-columns: 1fr;
        }

        .view-toggle {
            flex-direction: column;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function toggleView(view) {
        const gridView = document.getElementById('grid-view');
        const tableView = document.getElementById('table-view');
        const gridBtn = document.getElementById('grid-btn');
        const tableBtn = document.getElementById('table-btn');
        
        if (view === 'grid') {
            gridView.style.display = 'grid';
            tableView.style.display = 'none';
            gridBtn.classList.add('active');
            tableBtn.classList.remove('active');
        } else {
            gridView.style.display = 'none';
            tableView.style.display = 'block';
            gridBtn.classList.remove('active');
            tableBtn.classList.add('active');
        }
    }
    
    function showBulkCreateModal() {
        document.getElementById('bulkCreateModal').style.display = 'flex';
    }
    
    function hideBulkCreateModal() {
        document.getElementById('bulkCreateModal').style.display = 'none';
    }
    
    // Close modal when clicking outside
    document.getElementById('bulkCreateModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideBulkCreateModal();
        }
    });
</script>
@endpush
@endsection
