<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_group_id',
        'color',
        'size',
        'price_adjustment',
        'quantity',
        'sku',
        'image',
        'is_available'
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'quantity' => 'integer',
        'is_available' => 'boolean',
    ];

    /**
     * Get the product group that owns this product variant
     */
    public function productGroup()
    {
        return $this->belongsTo(ProductGroup::class);
    }

    /**
     * Get the final price for this product variant
     */
    public function getFinalPriceAttribute()
    {
        return $this->productGroup->base_price + $this->price_adjustment;
    }

    /**
     * Get the full name including color and size
     */
    public function getFullNameAttribute()
    {
        return $this->productGroup->name . ' - ' . $this->color . ' - ' . $this->size;
    }

    /**
     * Check if product is in stock
     */
    public function getInStockAttribute()
    {
        return $this->is_available && $this->quantity > 0;
    }

    /**
     * Scope for available products
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)->where('quantity', '>', 0);
    }

    /**
     * Scope for specific color
     */
    public function scopeColor($query, $color)
    {
        return $query->where('color', $color);
    }

    /**
     * Scope for specific size
     */
    public function scopeSize($query, $size)
    {
        return $query->where('size', $size);
    }

    /**
     * Generate SKU automatically
     */
    public static function generateSku($productGroupId, $color, $size)
    {
        $productGroup = ProductGroup::find($productGroupId);
        $prefix = strtoupper(substr($productGroup->name, 0, 3));
        $colorCode = strtoupper(substr($color, 0, 2));
        $sizeCode = strtoupper($size);

        return $prefix . '-' . $colorCode . '-' . $sizeCode . '-' . time();
    }
}
