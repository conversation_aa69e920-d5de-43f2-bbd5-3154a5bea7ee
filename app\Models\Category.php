<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Category extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description'];

    // Relationship with product groups
    public function productGroups()
    {
        return $this->hasMany(ProductGroup::class);
    }

    // Get all products through product groups
    public function products()
    {
        return $this->hasManyThrough(Product::class, ProductGroup::class);
    }

    // علاقة مع الفئة الأب (للفئات الفرعية)
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    // علاقة مع الفئات الفرعية
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }
}
