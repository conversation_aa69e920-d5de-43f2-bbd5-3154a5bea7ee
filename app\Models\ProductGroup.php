<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductGroup extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category_id',
        'base_price',
        'is_active',
        'is_trending',
        'rating',
        'main_image'
    ];

    protected $casts = [
        'base_price' => 'decimal:2',
        'rating' => 'decimal:2',
        'is_active' => 'boolean',
        'is_trending' => 'boolean',
    ];

    /**
     * Get the category that owns the product group
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get all product variants for this group
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get available product variants
     */
    public function availableProducts()
    {
        return $this->hasMany(Product::class)->where('is_available', true)->where('quantity', '>', 0);
    }

    /**
     * Get the tags associated with this product group
     */
    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'product_group_tags');
    }

    /**
     * Get all unique colors available for this product group
     */
    public function getAvailableColorsAttribute()
    {
        return $this->products()
            ->where('is_available', true)
            ->where('quantity', '>', 0)
            ->distinct()
            ->pluck('color')
            ->toArray();
    }

    /**
     * Get all unique sizes available for this product group
     */
    public function getAvailableSizesAttribute()
    {
        return $this->products()
            ->where('is_available', true)
            ->where('quantity', '>', 0)
            ->distinct()
            ->pluck('size')
            ->toArray();
    }

    /**
     * Get the price range for this product group
     */
    public function getPriceRangeAttribute()
    {
        $products = $this->products()->where('is_available', true)->get();
        
        if ($products->isEmpty()) {
            return ['min' => $this->base_price, 'max' => $this->base_price];
        }

        $prices = $products->map(function ($product) {
            return $this->base_price + $product->price_adjustment;
        });

        return [
            'min' => $prices->min(),
            'max' => $prices->max()
        ];
    }

    /**
     * Get total stock for this product group
     */
    public function getTotalStockAttribute()
    {
        return $this->products()
            ->where('is_available', true)
            ->sum('quantity');
    }

    /**
     * Scope for active product groups
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for trending product groups
     */
    public function scopeTrending($query)
    {
        return $query->where('is_trending', true);
    }

    /**
     * Scope for product groups with available stock
     */
    public function scopeInStock($query)
    {
        return $query->whereHas('products', function ($q) {
            $q->where('is_available', true)->where('quantity', '>', 0);
        });
    }

    /**
     * Get similar product groups based on shared tags
     */
    public function getSimilarProducts($limit = 6)
    {
        $tagIds = $this->tags()->pluck('tag_id')->toArray();
        
        if (empty($tagIds)) {
            return collect();
        }

        return self::whereHas('tags', function ($query) use ($tagIds) {
            $query->whereIn('tag_id', $tagIds);
        })
        ->where('id', '!=', $this->id)
        ->where('is_active', true)
        ->inStock()
        ->limit($limit)
        ->get();
    }
}
