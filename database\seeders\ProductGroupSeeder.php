<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductGroup;
use App\Models\Product;
use App\Models\Category;
use App\Models\Tag;

class ProductGroupSeeder extends Seeder
{
    public function run(): void
    {
        $productGroups = [
            [
                'name' => 'Classic Cotton T-Shirt',
                'description' => 'Premium quality 100% cotton t-shirt with comfortable fit. Perfect for casual wear and everyday comfort.',
                'base_price' => 29.99,
                'category_name' => 'Men\'s Clothing',
                'is_active' => true,
                'is_trending' => true,
                'rating' => 4.5,
                'main_image' => 'tshirt-main.jpg',
                'tags' => ['Cotton', 'Casual', 'New Arrival', 'Best Seller'],
                'variants' => [
                    ['color' => 'White', 'size' => 'S', 'price_adjustment' => 0, 'quantity' => 25],
                    ['color' => 'White', 'size' => 'M', 'price_adjustment' => 0, 'quantity' => 30],
                    ['color' => 'White', 'size' => 'L', 'price_adjustment' => 0, 'quantity' => 20],
                    ['color' => 'Black', 'size' => 'S', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Black', 'size' => 'M', 'price_adjustment' => 0, 'quantity' => 25],
                    ['color' => 'Black', 'size' => 'L', 'price_adjustment' => 0, 'quantity' => 18],
                    ['color' => 'Navy Blue', 'size' => 'M', 'price_adjustment' => 2.00, 'quantity' => 12],
                    ['color' => 'Navy Blue', 'size' => 'L', 'price_adjustment' => 2.00, 'quantity' => 10],
                ]
            ],
            [
                'name' => 'Slim Fit Denim Jeans',
                'description' => 'Modern slim fit jeans made from premium denim. Comfortable stretch fabric with classic styling.',
                'base_price' => 79.99,
                'category_name' => 'Men\'s Clothing',
                'is_active' => true,
                'is_trending' => false,
                'rating' => 4.2,
                'main_image' => 'jeans-main.jpg',
                'tags' => ['Denim', 'Casual', 'Stretch', 'Modern'],
                'variants' => [
                    ['color' => 'Dark Blue', 'size' => '30', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Dark Blue', 'size' => '32', 'price_adjustment' => 0, 'quantity' => 20],
                    ['color' => 'Dark Blue', 'size' => '34', 'price_adjustment' => 0, 'quantity' => 18],
                    ['color' => 'Light Blue', 'size' => '30', 'price_adjustment' => 5.00, 'quantity' => 10],
                    ['color' => 'Light Blue', 'size' => '32', 'price_adjustment' => 5.00, 'quantity' => 12],
                    ['color' => 'Black', 'size' => '32', 'price_adjustment' => 10.00, 'quantity' => 8],
                ]
            ],
            [
                'name' => 'Summer Floral Dress',
                'description' => 'Beautiful floral print dress perfect for summer occasions. Lightweight and breathable fabric.',
                'base_price' => 59.99,
                'category_name' => 'Women\'s Clothing',
                'is_active' => true,
                'is_trending' => true,
                'rating' => 4.7,
                'main_image' => 'dress-main.jpg',
                'tags' => ['Summer', 'Floral', 'Casual', 'Breathable', 'Trending'],
                'variants' => [
                    ['color' => 'Pink Floral', 'size' => 'XS', 'price_adjustment' => 0, 'quantity' => 8],
                    ['color' => 'Pink Floral', 'size' => 'S', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Pink Floral', 'size' => 'M', 'price_adjustment' => 0, 'quantity' => 20],
                    ['color' => 'Blue Floral', 'size' => 'S', 'price_adjustment' => 0, 'quantity' => 12],
                    ['color' => 'Blue Floral', 'size' => 'M', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Blue Floral', 'size' => 'L', 'price_adjustment' => 0, 'quantity' => 10],
                ]
            ],
            [
                'name' => 'Kids Rainbow Hoodie',
                'description' => 'Colorful and cozy hoodie for kids. Soft cotton blend with fun rainbow design.',
                'base_price' => 39.99,
                'category_name' => 'Kids & Baby',
                'is_active' => true,
                'is_trending' => true,
                'rating' => 4.8,
                'main_image' => 'kids-hoodie-main.jpg',
                'tags' => ['Kids', 'Cotton', 'Colorful', 'Cozy', 'New Arrival'],
                'variants' => [
                    ['color' => 'Rainbow', 'size' => '4T', 'price_adjustment' => 0, 'quantity' => 12],
                    ['color' => 'Rainbow', 'size' => '5T', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Rainbow', 'size' => '6T', 'price_adjustment' => 2.00, 'quantity' => 10],
                    ['color' => 'Pink Rainbow', 'size' => '4T', 'price_adjustment' => 0, 'quantity' => 8],
                    ['color' => 'Pink Rainbow', 'size' => '5T', 'price_adjustment' => 0, 'quantity' => 10],
                ]
            ],
            [
                'name' => 'Athletic Running Shoes',
                'description' => 'High-performance running shoes with advanced cushioning and breathable mesh upper.',
                'base_price' => 129.99,
                'category_name' => 'Shoes & Footwear',
                'is_active' => true,
                'is_trending' => false,
                'rating' => 4.4,
                'main_image' => 'running-shoes-main.jpg',
                'tags' => ['Sports', 'Running', 'Breathable', 'Cushioned', 'Athletic'],
                'variants' => [
                    ['color' => 'Black/White', 'size' => '8', 'price_adjustment' => 0, 'quantity' => 12],
                    ['color' => 'Black/White', 'size' => '9', 'price_adjustment' => 0, 'quantity' => 15],
                    ['color' => 'Black/White', 'size' => '10', 'price_adjustment' => 0, 'quantity' => 18],
                    ['color' => 'Blue/Gray', 'size' => '8', 'price_adjustment' => 10.00, 'quantity' => 8],
                    ['color' => 'Blue/Gray', 'size' => '9', 'price_adjustment' => 10.00, 'quantity' => 10],
                    ['color' => 'Red/Black', 'size' => '9', 'price_adjustment' => 15.00, 'quantity' => 6],
                ]
            ],
        ];

        foreach ($productGroups as $productData) {
            // Find category
            $category = Category::where('name', $productData['category_name'])->first();
            if (!$category) {
                $this->command->warn("Category '{$productData['category_name']}' not found. Skipping product '{$productData['name']}'");
                continue;
            }

            // Create product group
            $productGroup = ProductGroup::create([
                'name' => $productData['name'],
                'description' => $productData['description'],
                'category_id' => $category->id,
                'base_price' => $productData['base_price'],
                'is_active' => $productData['is_active'],
                'is_trending' => $productData['is_trending'],
                'rating' => $productData['rating'],
                'main_image' => $productData['main_image'],
            ]);

            // Attach tags
            $tagIds = [];
            foreach ($productData['tags'] as $tagName) {
                $tag = Tag::where('name', $tagName)->first();
                if ($tag) {
                    $tagIds[] = $tag->id;
                }
            }
            if (!empty($tagIds)) {
                $productGroup->tags()->attach($tagIds);
            }

            // Create variants
            foreach ($productData['variants'] as $variantData) {
                Product::create([
                    'product_group_id' => $productGroup->id,
                    'color' => $variantData['color'],
                    'size' => $variantData['size'],
                    'price_adjustment' => $variantData['price_adjustment'],
                    'quantity' => $variantData['quantity'],
                    'sku' => $this->generateSKU($productGroup->name, $variantData['color'], $variantData['size']),
                    'is_available' => true,
                ]);
            }

            $this->command->info("Created product group: {$productGroup->name} with " . count($productData['variants']) . " variants");
        }

        $this->command->info('Product groups and variants created successfully!');
    }

    private function generateSKU($productName, $color, $size): string
    {
        $productCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $productName), 0, 3));
        $colorCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $color), 0, 2));
        $sizeCode = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $size));
        $timestamp = time();
        
        return "{$productCode}-{$colorCode}-{$sizeCode}-{$timestamp}";
    }
}
