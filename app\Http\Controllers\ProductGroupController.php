<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProductGroup;
use App\Models\Product;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Support\Facades\DB;

class ProductGroupController extends Controller
{
    /**
     * Display a listing of product groups with pagination
     */
    public function index(Request $request)
    {
        $perPage = min($request->get('per_page', 12), 50);
        
        $query = ProductGroup::with(['category', 'tags'])
                            ->where('is_active', true);
        
        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }
        
        // Filter by trending
        if ($request->has('trending') && $request->trending) {
            $query->where('is_trending', true);
        }
        
        // Search in name
        if ($request->has('search')) {
            $query->where('name', 'like', "%{$request->search}%");
        }
        
        // Filter by tags
        if ($request->has('tags')) {
            $tagIds = is_array($request->tags) ? $request->tags : explode(',', $request->tags);
            $query->whereHas('tags', function ($q) use ($tagIds) {
                $q->whereIn('tag_id', $tagIds);
            });
        }
        
        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('base_price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('base_price', '<=', $request->max_price);
        }
        
        // Only show products with stock
        if ($request->get('in_stock', false)) {
            $query->inStock();
        }
        
        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['name', 'base_price', 'rating', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }
        
        return $query->paginate($perPage);
    }

    /**
     * Display product groups for homepage (optimized)
     */
    public function homepage(Request $request)
    {
        $perPage = min($request->get('per_page', 12), 24);
        
        $productGroups = ProductGroup::select(['id', 'name', 'base_price', 'rating', 'is_trending', 'main_image'])
                                   ->where('is_active', true)
                                   ->inStock()
                                   ->orderBy('is_trending', 'desc')
                                   ->orderBy('rating', 'desc')
                                   ->orderBy('created_at', 'desc')
                                   ->paginate($perPage);
        
        return response()->json([
            'data' => $productGroups->items(),
            'pagination' => [
                'current_page' => $productGroups->currentPage(),
                'last_page' => $productGroups->lastPage(),
                'per_page' => $productGroups->perPage(),
                'total' => $productGroups->total(),
                'has_more_pages' => $productGroups->hasMorePages(),
            ]
        ]);
    }

    /**
     * Display the specified product group with all variants
     */
    public function show(ProductGroup $productGroup)
    {
        $productGroup->load([
            'category',
            'tags',
            'products' => function ($query) {
                $query->where('is_available', true)->orderBy('color')->orderBy('size');
            }
        ]);

        // Get similar products
        $similarProducts = $productGroup->getSimilarProducts(6);

        return response()->json([
            'product_group' => $productGroup,
            'available_colors' => $productGroup->available_colors,
            'available_sizes' => $productGroup->available_sizes,
            'price_range' => $productGroup->price_range,
            'total_stock' => $productGroup->total_stock,
            'similar_products' => $similarProducts
        ]);
    }

    /**
     * Store a new product group with variants
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'base_price' => 'required|numeric|min:0',
            'is_active' => 'boolean',
            'is_trending' => 'boolean',
            'rating' => 'nullable|numeric|min:0|max:5',
            'main_image' => 'nullable|string',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
            'variants' => 'required|array|min:1',
            'variants.*.color' => 'required|string|max:50',
            'variants.*.size' => 'required|string|max:20',
            'variants.*.price_adjustment' => 'nullable|numeric',
            'variants.*.quantity' => 'required|integer|min:0',
            'variants.*.image' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Create product group
            $productGroup = ProductGroup::create([
                'name' => $request->name,
                'description' => $request->description,
                'category_id' => $request->category_id,
                'base_price' => $request->base_price,
                'is_active' => $request->get('is_active', true),
                'is_trending' => $request->get('is_trending', false),
                'rating' => $request->get('rating', 0),
                'main_image' => $request->main_image,
            ]);

            // Attach tags
            if ($request->has('tags')) {
                $productGroup->tags()->sync($request->tags);
            }

            // Create variants
            foreach ($request->variants as $variant) {
                $sku = Product::generateSku($productGroup->id, $variant['color'], $variant['size']);
                
                Product::create([
                    'product_group_id' => $productGroup->id,
                    'color' => $variant['color'],
                    'size' => $variant['size'],
                    'price_adjustment' => $variant['price_adjustment'] ?? 0,
                    'quantity' => $variant['quantity'],
                    'sku' => $sku,
                    'image' => $variant['image'] ?? null,
                    'is_available' => true,
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Product group created successfully',
                'product_group' => $productGroup->load(['category', 'tags', 'products'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error creating product group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified product group
     */
    public function update(Request $request, ProductGroup $productGroup)
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'sometimes|exists:categories,id',
            'base_price' => 'sometimes|numeric|min:0',
            'is_active' => 'boolean',
            'is_trending' => 'boolean',
            'rating' => 'nullable|numeric|min:0|max:5',
            'main_image' => 'nullable|string',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
        ]);

        try {
            DB::beginTransaction();

            $productGroup->update($request->only([
                'name', 'description', 'category_id', 'base_price',
                'is_active', 'is_trending', 'rating', 'main_image'
            ]));

            // Update tags
            if ($request->has('tags')) {
                $productGroup->tags()->sync($request->tags);
            }

            DB::commit();

            return response()->json([
                'message' => 'Product group updated successfully',
                'product_group' => $productGroup->load(['category', 'tags', 'products'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Error updating product group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified product group
     */
    public function destroy(ProductGroup $productGroup)
    {
        try {
            $productGroup->delete(); // This will cascade delete all variants
            
            return response()->json([
                'message' => 'Product group deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Error deleting product group',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get form data for creating/editing product groups
     */
    public function getFormData()
    {
        return response()->json([
            'categories' => Category::all(),
            'tags' => Tag::all(),
        ]);
    }
}
