<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AdminAuthController extends Controller
{
    /**
     * Show the admin login form.
     */
    public function showLoginForm()
    {
        return view('admin.auth.login');
    }

    /**
     * Handle admin login request.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        // Find admin by email
        $admin = Admin::where('email', $credentials['email'])->first();

        if (!$admin) {
            throw ValidationException::withMessages([
                'email' => ['Admin account not found.'],
            ]);
        }

        // Check if admin can login
        if (!$admin->canLogin()) {
            $message = 'Account is ';
            if (!$admin->is_active) {
                $message .= 'inactive.';
            } elseif (!$admin->can_login) {
                $message .= 'disabled.';
            } elseif ($admin->isLocked()) {
                $message .= 'temporarily locked.';
            }

            throw ValidationException::withMessages([
                'email' => [$message],
            ]);
        }

        // Verify password
        if (!Hash::check($credentials['password'], $admin->password)) {
            // Increment login attempts
            $admin->increment('login_attempts');
            
            // Lock account after 5 failed attempts
            if ($admin->login_attempts >= 5) {
                $admin->update(['locked_until' => now()->addMinutes(30)]);
            }

            throw ValidationException::withMessages([
                'email' => ['Invalid credentials.'],
            ]);
        }

        // Reset login attempts on successful login
        $admin->update([
            'login_attempts' => 0,
            'locked_until' => null,
            'last_login_at' => now(),
            'last_login_ip' => $request->ip(),
        ]);

        // Login using admin guard
        Auth::guard('admin')->login($admin, $remember);

        $request->session()->regenerate();

        return redirect()->intended(route('admin.dashboard'))
            ->with('success', 'Welcome back, ' . $admin->name . '!');
    }

    /**
     * Handle admin logout.
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show admin dashboard.
     */
    public function dashboard()
    {
        $admin = Auth::guard('admin')->user();
        
        $stats = [
            'total_admins' => Admin::count(),
            'active_admins' => Admin::where('is_active', true)->count(),
            'total_users' => \App\Models\User::count(),
            'total_products' => \App\Models\ProductGroup::count(),
            'total_categories' => \App\Models\Category::count(),
            'total_tags' => \App\Models\Tag::count(),
        ];

        $recent_admins = Admin::latest()->take(5)->get();

        return view('admin.dashboard', compact('admin', 'stats', 'recent_admins'));
    }
}
