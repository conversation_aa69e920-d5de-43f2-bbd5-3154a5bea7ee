# API المنتجات مع Pagination

## نظرة عامة

تم إنشاء API endpoints للمنتجات مع دعم Pagination لتحسين الأداء وسرعة التحميل.

## Endpoints المتاحة

### 1. عرض جميع المنتجات (مع فلترة وبحث)

```
GET /api/products
```

**المعاملات المتاحة:**

-   `page` - رقم الصفحة (افتراضي: 1)
-   `per_page` - عدد المنتجات في الصفحة (افتراضي: 10، حد أقصى: 50)
-   `search` - البحث في اسم المنتج
-   `category_id` - فلترة حسب الفئة
-   `trending` - عرض المنتجات الرائجة فقط (true/false)
-   `sort_by` - ترتيب حسب (name, price, rating, created_at)
-   `sort_order` - اتجاه الترتيب (asc, desc)

**مثال:**

```
GET /api/products?page=1&per_page=12&search=هاتف&sort_by=price&sort_order=asc
```

### 2. منتجات الصفحة الرئيسية (محسّن للسرعة)

```
GET /api/products/homepage
```

**المعاملات المتاحة:**

-   `page` - رقم الصفحة (افتراضي: 1)
-   `per_page` - عدد المنتجات في الصفحة (افتراضي: 12، حد أقصى: 24)

**مثال:**

```
GET /api/products/homepage?page=1&per_page=12
```

### 3. عرض منتج واحد

```
GET /api/products/{id}
```

## استجابة API

### للمنتجات العادية (Laravel Pagination):

```json
{
    "data": [
        {
            "id": 1,
            "name": "اسم المنتج",
            "description": "وصف المنتج",
            "price": 100.0,
            "rating": 4.5,
            "is_active": true,
            "is_trending": false,
            "category": {
                "id": 1,
                "name": "اسم الفئة"
            },
            "tags": [
                {
                    "id": 1,
                    "name": "تاغ"
                }
            ],
            "variants": [
                {
                    "id": 1,
                    "name": "متغير",
                    "price": 100.0,
                    "stock": 10
                }
            ],
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-01T00:00:00.000000Z"
        }
    ],
    "links": {
        "first": "http://localhost:8000/api/products?page=1",
        "last": "http://localhost:8000/api/products?page=10",
        "prev": null,
        "next": "http://localhost:8000/api/products?page=2"
    },
    "meta": {
        "current_page": 1,
        "from": 1,
        "last_page": 10,
        "per_page": 10,
        "to": 10,
        "total": 100
    }
}
```

### للصفحة الرئيسية (مبسط):

```json
{
    "data": [
        {
            "id": 1,
            "name": "اسم المنتج",
            "price": 100.0,
            "rating": 4.5,
            "is_trending": false
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 10,
        "per_page": 12,
        "total": 120,
        "has_more_pages": true
    }
}
```

## مميزات الـ API

### 1. الأداء المحسّن

-   **Pagination**: تحميل المنتجات بالتدريج
-   **Lazy Loading**: تحميل العلاقات عند الحاجة فقط
-   **Selective Fields**: في الصفحة الرئيسية يتم تحميل الحقول الأساسية فقط

### 2. البحث والفلترة

-   البحث في اسم المنتج
-   فلترة حسب الفئة
-   عرض المنتجات الرائجة
-   ترتيب متعدد الخيارات

### 3. الأمان

-   حد أقصى لعدد المنتجات في الصفحة
-   تنظيف المدخلات
-   عرض المنتجات النشطة فقط

## كيفية الاستخدام في Frontend

### JavaScript/Ajax مثال:

```javascript
async function loadProducts(page = 1) {
    const response = await fetch(
        `/api/products/homepage?page=${page}&per_page=12`
    );
    const data = await response.json();

    // عرض المنتجات
    displayProducts(data.data);

    // عرض أزرار التنقل
    displayPagination(data.pagination);
}

function displayPagination(pagination) {
    const { current_page, last_page, has_more_pages } = pagination;

    // إنشاء أزرار التنقل
    if (current_page > 1) {
        // زر السابق
    }

    if (has_more_pages) {
        // زر التالي
    }
}
```

### React مثال:

```jsx
const [products, setProducts] = useState([]);
const [pagination, setPagination] = useState({});
const [loading, setLoading] = useState(false);

const loadProducts = async (page = 1) => {
    setLoading(true);
    try {
        const response = await fetch(
            `/api/products/homepage?page=${page}&per_page=12`
        );
        const data = await response.json();

        setProducts(data.data);
        setPagination(data.pagination);
    } catch (error) {
        console.error("Error loading products:", error);
    } finally {
        setLoading(false);
    }
};
```

## اختبار الـ API

1. افتح ملف `test_api.html` في المتصفح
2. جرب الخيارات المختلفة للبحث والفلترة
3. اختبر التنقل بين الصفحات
4. قارن بين endpoint المنتجات العادي والصفحة الرئيسية

## إدارة المنتجات الشاملة

### إضافة منتج جديد مع كل العناصر

```
POST /api/product-management/create-complete
```

**مثال على البيانات المرسلة:**

```json
{
    "product": {
        "name": "قميص قطني",
        "description": "قميص قطني عالي الجودة",
        "price": 150.0,
        "is_active": true,
        "is_trending": false,
        "rating": 4.5
    },
    "category": {
        "name": "ملابس رجالية",
        "description": "فئة الملابس الرجالية"
    },
    "colors": [
        {
            "name": "أزرق",
            "hex_code": "#0000FF"
        },
        {
            "id": 1
        }
    ],
    "sizes": [
        {
            "name": "كبير",
            "abbreviation": "L"
        },
        {
            "id": 2
        }
    ],
    "tags": [
        {
            "name": "قطني"
        }
    ],
    "variants": [
        {
            "color_index": 0,
            "size_index": 0,
            "quantity": 10,
            "image": "path/to/image.jpg"
        }
    ]
}
```

### الحصول على بيانات النموذج

```
GET /api/product-management/form-data
```

### إضافة سريعة للعناصر

```
POST /api/product-management/quick-add
```

**مثال:**

```json
{
    "type": "category",
    "name": "إلكترونيات",
    "extra_data": {
        "description": "فئة الأجهزة الإلكترونية"
    }
}
```

### API endpoints للعناصر الفردية

#### الفئات

-   `GET /api/categories` - عرض جميع الفئات
-   `POST /api/categories` - إنشاء فئة جديدة
-   `GET /api/categories/{id}` - عرض فئة معينة
-   `PUT /api/categories/{id}` - تعديل فئة
-   `DELETE /api/categories/{id}` - حذف فئة

#### الألوان

-   `GET /api/colors` - عرض جميع الألوان
-   `POST /api/colors` - إنشاء لون جديد
-   `GET /api/colors/{id}` - عرض لون معين
-   `PUT /api/colors/{id}` - تعديل لون
-   `DELETE /api/colors/{id}` - حذف لون

#### الأحجام

-   `GET /api/sizes` - عرض جميع الأحجام
-   `POST /api/sizes` - إنشاء حجم جديد
-   `GET /api/sizes/{id}` - عرض حجم معين
-   `PUT /api/sizes/{id}` - تعديل حجم
-   `DELETE /api/sizes/{id}` - حذف حجم

#### التاغات

-   `GET /api/tags` - عرض جميع التاغات
-   `POST /api/tags` - إنشاء تاغ جديد
-   `GET /api/tags/{id}` - عرض تاغ معين
-   `PUT /api/tags/{id}` - تعديل تاغ
-   `DELETE /api/tags/{id}` - حذف تاغ

#### توليفات المنتجات

-   `GET /api/product-variants` - عرض جميع التوليفات
-   `POST /api/product-variants` - إنشاء توليفة جديدة
-   `GET /api/product-variants/{id}` - عرض توليفة معينة
-   `PUT /api/product-variants/{id}` - تعديل توليفة
-   `DELETE /api/product-variants/{id}` - حذف توليفة

## واجهة إدارة المنتجات

تم إنشاء واجهة HTML تفاعلية في ملف `product_management.html` تتيح:

1. **إضافة منتج جديد** مع كل العناصر المرتبطة
2. **إضافة سريعة** للفئات والألوان والأحجام والتاغات
3. **إنشاء التوليفات تلقائياً** بناءً على الألوان والأحجام
4. **واجهة سهلة الاستخدام** باللغة العربية

### كيفية الاستخدام:

1. افتح ملف `product_management.html` في المتصفح
2. املأ بيانات المنتج الأساسية
3. اختر فئة موجودة أو أنشئ فئة جديدة
4. أضف الألوان والأحجام المطلوبة
5. أضف التاغات (اختياري)
6. اضغط "إنشاء التوليفات" لإنشاء جميع التوليفات الممكنة
7. حدد الكمية لكل توليفة
8. اضغط "إنشاء المنتج"

## نصائح للاستخدام الأمثل

1. **للصفحة الرئيسية**: استخدم `/api/products/homepage` للحصول على أداء أفضل
2. **للبحث والفلترة**: استخدم `/api/products` مع المعاملات المطلوبة
3. **حجم الصفحة**: ابدأ بـ 12 منتج واضبط حسب الحاجة
4. **التحميل التدريجي**: استخدم Infinite Scroll أو Load More buttons
5. **التخزين المؤقت**: احفظ النتائج في localStorage لتحسين التجربة
6. **إدارة المنتجات**: استخدم واجهة `product_management.html` لإضافة المنتجات بسهولة
7. **التوليفات**: يمكن إنشاء توليفات متعددة لنفس المنتج بألوان وأحجام مختلفة
