<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProductGroup;
use App\Models\Category;
use App\Models\Tag;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Middleware is applied in routes instead
    }

    /**
     * Show the application dashboard.
     */
    public function index()
    {
        $stats = [
            'total_products' => ProductGroup::count(),
            'active_products' => ProductGroup::where('is_active', true)->count(),
            'trending_products' => ProductGroup::where('is_trending', true)->count(),
            'total_categories' => Category::count(),
            'total_tags' => Tag::count(),
        ];

        $recent_products = ProductGroup::with('category')
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard', compact('stats', 'recent_products'));
    }
}
