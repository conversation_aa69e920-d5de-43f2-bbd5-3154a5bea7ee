<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Admin Dashboard - {{ config('app.name', 'Laravel') }}</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .admin-navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-logo {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
            text-decoration: none;
        }

        .admin-nav-links {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-link {
            color: #666;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: #f8f9fa;
            color: #667eea;
        }

        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .admin-title {
            font-size: 2.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .admin-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        .admin-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            display: inline-block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        .admin-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .admin-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .admin-item:last-child {
            border-bottom: none;
        }

        .admin-item-info h4 {
            margin: 0 0 0.25rem 0;
            color: #333;
            font-size: 1rem;
        }

        .admin-item-info p {
            margin: 0;
            color: #666;
            font-size: 0.875rem;
        }

        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .role-super_admin { background: #dc3545; color: white; }
        .role-admin { background: #007bff; color: white; }
        .role-moderator { background: #28a745; color: white; }
        .role-editor { background: #ffc107; color: #333; }

        .quick-actions {
            display: grid;
            gap: 1rem;
        }

        .action-btn {
            display: block;
            padding: 1rem;
            background: #f8f9fa;
            color: #333;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .alert {
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }

            .admin-title {
                font-size: 2rem;
            }

            .stat-number {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="admin-navbar">
        <div class="navbar-content">
            <a href="{{ route('admin.dashboard') }}" class="admin-logo">
                👨‍💼 Admin Panel
            </a>

            <div class="admin-nav-links">
                @if($admin->hasPermission('manage_products'))
                    <a href="{{ route('admin.products.index') }}" class="nav-link">📦 Products</a>
                @endif
                <a href="{{ url('/admin_panel.html') }}" class="nav-link">Legacy Panel</a>
                <a href="{{ url('/') }}" class="nav-link">View Website</a>
                <span class="nav-link">{{ $admin->name }} ({{ ucfirst($admin->role) }})</span>
                <form action="{{ route('admin.logout') }}" method="POST" style="display: inline;">
                    @csrf
                    <button type="submit" class="btn btn-danger">Logout</button>
                </form>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        <!-- Flash Messages -->
        @if (session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        <!-- Header -->
        <div class="admin-header">
            <h1 class="admin-title">Admin Dashboard</h1>
            <p class="admin-subtitle">Welcome back, {{ $admin->name }}! Manage your system from here.</p>
            <div class="admin-info">
                <strong>Role:</strong> {{ $admin->getRoleDisplayAttribute() }} | 
                <strong>Last Login:</strong> {{ $admin->last_login_at ? $admin->last_login_at->diffForHumans() : 'Never' }} |
                <strong>Permissions:</strong> {{ count($admin->permissions ?? []) }} granted
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-icon">👨‍💼</span>
                <div class="stat-number">{{ $stats['total_admins'] }}</div>
                <div class="stat-label">Total Admins</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-number">{{ $stats['active_admins'] }}</div>
                <div class="stat-label">Active Admins</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-number">{{ $stats['total_users'] }}</div>
                <div class="stat-label">Total Users</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">📦</span>
                <div class="stat-number">{{ $stats['total_products'] }}</div>
                <div class="stat-label">Products</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">📂</span>
                <div class="stat-number">{{ $stats['total_categories'] }}</div>
                <div class="stat-label">Categories</div>
            </div>

            <div class="stat-card">
                <span class="stat-icon">🏷️</span>
                <div class="stat-number">{{ $stats['total_tags'] }}</div>
                <div class="stat-label">Tags</div>
            </div>
        </div>

        <!-- Content Grid -->
        <div class="content-grid">
            <!-- Recent Admins -->
            <div class="card">
                <div class="card-header">
                    Recent Admin Accounts
                </div>
                <div class="card-body">
                    @if($recent_admins->count() > 0)
                        <ul class="admin-list">
                            @foreach($recent_admins as $recentAdmin)
                                <li class="admin-item">
                                    <div class="admin-item-info">
                                        <h4>{{ $recentAdmin->name }}</h4>
                                        <p>{{ $recentAdmin->email }} • Created {{ $recentAdmin->created_at->diffForHumans() }}</p>
                                    </div>
                                    <span class="role-badge role-{{ $recentAdmin->role }}">{{ ucfirst($recentAdmin->role) }}</span>
                                </li>
                            @endforeach
                        </ul>
                    @else
                        <p>No admin accounts found.</p>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    Quick Actions
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        @if($admin->hasPermission('manage_products'))
                            <a href="{{ route('admin.products.index') }}" class="action-btn">
                                📦 Manage Products
                            </a>
                            <a href="{{ route('admin.products.create') }}" class="action-btn">
                                ➕ Add New Product
                            </a>
                        @endif

                        @if($admin->hasPermission('manage_users'))
                            <a href="{{ route('user.dashboard') }}" class="action-btn">
                                👥 View Users
                            </a>
                        @endif

                        @if($admin->hasPermission('view_analytics'))
                            <a href="{{ url('/test_new_structure.html') }}" class="action-btn">
                                📊 Test API
                            </a>
                        @endif

                        <a href="{{ url('/admin_panel.html') }}" class="action-btn">
                            🔧 Legacy Panel
                        </a>

                        <a href="{{ url('/') }}" class="action-btn">
                            🌐 View Website
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden logout form -->
    <form id="logout-form" action="{{ route('admin.logout') }}" method="POST" style="display: none;">
        @csrf
    </form>
</body>
</html>
