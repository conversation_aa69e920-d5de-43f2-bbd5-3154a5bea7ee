<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;

class CategoryController extends Controller
{
    // عرض جميع الفئات
    public function index()
    {
        return Category::all();
    }

    // إنشاء فئة جديدة
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name',
            'description' => 'nullable|string',
        ]);

        $category = Category::create($data);

        return response()->json($category, 201);
    }

    // عرض فئة معينة
    public function show(Category $category)
    {
        return $category;
    }

    // تعديل فئة
    public function update(Request $request, Category $category)
    {
        $data = $request->validate([
            'name' => 'sometimes|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string',
        ]);

        $category->update($data);

        return response()->json($category);
    }

    // حذف فئة
    public function destroy(Category $category)
    {
        $category->delete();
        return response()->json(null, 204);
    }
}
