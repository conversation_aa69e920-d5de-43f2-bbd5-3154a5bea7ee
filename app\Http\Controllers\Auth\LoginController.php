<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        // Middleware is applied in routes instead
    }

    /**
     * Show the login form.
     */
    public function showLoginForm()
    {
        return view('auth.login');
    }

    /**
     * Handle a login request to the application.
     * Checks both users and admins tables and redirects accordingly.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
        ]);

        $email = $request->input('email');
        $password = $request->input('password');
        $remember = $request->boolean('remember');

        // First, check if user exists in admins table
        $admin = Admin::where('email', $email)->first();
        if ($admin && $admin->password && Hash::check($password, $admin->password)) {
            // Login as admin
            Auth::guard('admin')->login($admin, $remember);
            $request->session()->regenerate();

            $adminName = $admin->first_name ?: $admin->name;
            return redirect()->intended(route('admin.dashboard'))
                ->with('success', "مرحباً بك في لوحة الإدارة، {$adminName}!");
        }

        // If not admin, check regular users table
        $user = User::where('email', $email)->first();
        if ($user && $user->password && Hash::check($password, $user->password)) {
            // Login as regular user
            Auth::guard('web')->login($user, $remember);
            $request->session()->regenerate();

            return redirect()->intended(route('home'))
                ->with('success', "مرحباً بك، {$user->name}!");
        }

        // If neither admin nor user found, return error
        throw ValidationException::withMessages([
            'email' => ['البيانات المدخلة غير صحيحة. تأكد من البريد الإلكتروني وكلمة المرور.'],
        ]);
    }

    /**
     * Log the user out of the application.
     * Handles both regular users and admins.
     */
    public function logout(Request $request)
    {
        // Check if admin is logged in
        if (Auth::guard('admin')->check()) {
            Auth::guard('admin')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')
                ->with('success', 'تم تسجيل الخروج بنجاح من لوحة الإدارة.');
        }

        // Check if regular user is logged in
        if (Auth::guard('web')->check()) {
            Auth::guard('web')->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('login')
                ->with('success', 'تم تسجيل الخروج بنجاح.');
        }

        // If no one is logged in, just redirect to login
        return redirect()->route('login')
            ->with('info', 'لم تكن مسجلاً للدخول.');
    }
}
