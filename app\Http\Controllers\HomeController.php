<?php

namespace App\Http\Controllers;

use App\Models\ProductGroup;
use App\Models\Category;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Show the application homepage.
     */
    public function index()
    {
        // Get featured/trending products
        $featuredProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('is_trending', true)
            ->latest()
            ->take(8)
            ->get();

        // Get latest products
        $latestProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->latest()
            ->take(12)
            ->get();

        // Get categories with product counts
        $categories = Category::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->where('is_active', true)
        ->orderBy('product_groups_count', 'desc')
        ->take(8)
        ->get();

        // Get popular tags
        $popularTags = Tag::withCount(['productGroups' => function($query) {
            $query->where('is_active', true);
        }])
        ->orderBy('product_groups_count', 'desc')
        ->take(10)
        ->get();

        // Get statistics for hero section
        $stats = [
            'total_products' => ProductGroup::where('is_active', true)->count(),
            'total_categories' => Category::where('is_active', true)->count(),
            'total_variants' => ProductGroup::where('is_active', true)
                ->withCount('products')
                ->get()
                ->sum('products_count'),
        ];

        return view('home', compact(
            'featuredProducts',
            'latestProducts', 
            'categories',
            'popularTags',
            'stats'
        ));
    }

    /**
     * Show products page with filtering and search.
     */
    public function products(Request $request)
    {
        $query = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('tags', function($tagQuery) use ($search) {
                      $tagQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Tag filter
        if ($request->filled('tag')) {
            $query->whereHas('tags', function($tagQuery) use ($request) {
                $tagQuery->where('tags.id', $request->tag);
            });
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('base_price', '>=', $request->min_price);
        }
        if ($request->filled('max_price')) {
            $query->where('base_price', '<=', $request->max_price);
        }

        // Rating filter
        if ($request->filled('rating')) {
            $query->where('rating', '>=', $request->rating);
        }

        // Trending filter
        if ($request->filled('trending') && $request->trending === 'yes') {
            $query->where('is_trending', true);
        }

        // Sorting
        $sort = $request->get('sort', 'latest');
        switch ($sort) {
            case 'price_low':
                $query->orderBy('base_price', 'asc');
                break;
            case 'price_high':
                $query->orderBy('base_price', 'desc');
                break;
            case 'rating':
                $query->orderBy('rating', 'desc');
                break;
            case 'name':
                $query->orderBy('name', 'asc');
                break;
            case 'trending':
                $query->orderBy('is_trending', 'desc')->orderBy('created_at', 'desc');
                break;
            default:
                $query->latest();
        }

        $products = $query->paginate(12);

        // Get filter options
        $categories = Category::where('is_active', true)
            ->withCount(['productGroups' => function($q) {
                $q->where('is_active', true);
            }])
            ->orderBy('name')
            ->get();

        $tags = Tag::withCount(['productGroups' => function($q) {
            $q->where('is_active', true);
        }])
        ->having('product_groups_count', '>', 0)
        ->orderBy('name')
        ->get();

        // Price range
        $priceRange = ProductGroup::where('is_active', true)
            ->selectRaw('MIN(base_price) as min_price, MAX(base_price) as max_price')
            ->first();

        return view('products.index', compact(
            'products',
            'categories',
            'tags',
            'priceRange'
        ));
    }

    /**
     * Show product details.
     */
    public function productDetails(ProductGroup $product)
    {
        if (!$product->is_active) {
            abort(404, 'Product not found.');
        }

        $product->load(['category', 'tags', 'products' => function($query) {
            $query->where('is_available', true)->orderBy('color')->orderBy('size');
        }]);

        // Get related products
        $relatedProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->take(4)
            ->get();

        // Get products with same tags
        $similarProducts = ProductGroup::with(['category', 'tags', 'products'])
            ->where('is_active', true)
            ->where('id', '!=', $product->id)
            ->whereHas('tags', function($query) use ($product) {
                $query->whereIn('tags.id', $product->tags->pluck('id'));
            })
            ->take(4)
            ->get();

        // Check if user has this in wishlist (if authenticated)
        $inWishlist = false;
        if (Auth::check()) {
            // We'll implement wishlist functionality later
            $inWishlist = false;
        }

        return view('products.show', compact(
            'product',
            'relatedProducts',
            'similarProducts',
            'inWishlist'
        ));
    }

    /**
     * Search products (AJAX endpoint).
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $products = ProductGroup::with(['category'])
            ->where('is_active', true)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->take(10)
            ->get()
            ->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category->name,
                    'price' => number_format($product->base_price, 2),
                    'image' => $product->main_image,
                    'url' => route('product.details', $product),
                ];
            });

        return response()->json($products);
    }
}
