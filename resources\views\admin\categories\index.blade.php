@extends('admin.layouts.app')

@section('title', 'Category Management')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">📂 Category Management</h1>
            <p class="page-subtitle">Organize your products with categories</p>
        </div>
        <div class="header-actions">
            @if($admin->hasPermission('manage_categories'))
                <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                    ➕ Add Category
                </a>
            @endif
            @if($admin->hasPermission('view_analytics'))
                <a href="{{ route('admin.categories.stats') }}" class="btn btn-outline">
                    📊 Statistics
                </a>
            @endif
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filters-card">
    <form method="GET" action="{{ route('admin.categories.index') }}" class="filters-form">
        <div class="filter-group">
            <input type="text" name="search" placeholder="Search categories..." 
                   value="{{ request('search') }}" class="filter-input">
        </div>
        
        <div class="filter-group">
            <select name="status" class="filter-select">
                <option value="">All Status</option>
                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
            </select>
        </div>
        
        <button type="submit" class="btn btn-secondary">🔍 Filter</button>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline">Clear</a>
    </form>
</div>

<!-- Categories Table -->
<div class="table-card">
    <div class="table-header">
        <h3>Categories ({{ $categories->total() }})</h3>
        <div class="table-actions">
            @if($admin->hasPermission('export_data'))
                <button class="btn btn-outline btn-sm">📊 Export</button>
            @endif
        </div>
    </div>
    
    <div class="table-responsive">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Category Name</th>
                    <th>Description</th>
                    <th>Products</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($categories as $category)
                    <tr>
                        <td>
                            <div class="category-info">
                                <h4>{{ $category->name }}</h4>
                                <small class="category-id">ID: {{ $category->id }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="description-cell">
                                {{ $category->description ? Str::limit($category->description, 80) : 'No description' }}
                            </div>
                        </td>
                        <td>
                            <div class="products-count">
                                <span class="count-number">{{ $category->product_groups_count }}</span>
                                <small class="count-label">products</small>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge {{ $category->is_active ? 'active' : 'inactive' }}">
                                {{ $category->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td>
                            <div class="date-info">
                                <span class="date">{{ $category->created_at->format('M d, Y') }}</span>
                                <small class="time">{{ $category->created_at->diffForHumans() }}</small>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="{{ route('admin.categories.show', $category) }}" 
                                   class="btn btn-sm btn-outline" title="View">👁️</a>
                                
                                @if($admin->hasPermission('manage_categories'))
                                    <a href="{{ route('admin.categories.edit', $category) }}" 
                                       class="btn btn-sm btn-primary" title="Edit">✏️</a>
                                    
                                    <form action="{{ route('admin.categories.toggle-status', $category) }}" 
                                          method="POST" style="display: inline;">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-sm btn-warning" 
                                                title="{{ $category->is_active ? 'Deactivate' : 'Activate' }}">
                                            {{ $category->is_active ? '⏸️' : '▶️' }}
                                        </button>
                                    </form>
                                @endif
                                
                                @if($admin->hasPermission('delete_data'))
                                    <form action="{{ route('admin.categories.destroy', $category) }}" 
                                          method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this category? This action cannot be undone.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-sm btn-danger" title="Delete">🗑️</button>
                                    </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="no-data">
                            <div class="empty-state">
                                <div class="empty-icon">📂</div>
                                <h3>No categories found</h3>
                                <p>Start by creating your first product category.</p>
                                @if($admin->hasPermission('manage_categories'))
                                    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                                        ➕ Create First Category
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if($categories->hasPages())
        <div class="pagination-wrapper">
            {{ $categories->withQueryString()->links() }}
        </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="stats-row">
    <div class="stat-card">
        <div class="stat-icon">📂</div>
        <div class="stat-info">
            <div class="stat-number">{{ $categories->total() }}</div>
            <div class="stat-label">Total Categories</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-info">
            <div class="stat-number">{{ $categories->where('is_active', true)->count() }}</div>
            <div class="stat-label">Active Categories</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-info">
            <div class="stat-number">{{ $categories->where('product_groups_count', '>', 0)->count() }}</div>
            <div class="stat-label">With Products</div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-info">
            <div class="stat-number">{{ $categories->sum('product_groups_count') }}</div>
            <div class="stat-label">Total Products</div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .category-info h4 {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #333;
    }

    .category-id {
        color: #666;
        font-size: 0.8rem;
    }

    .description-cell {
        color: #666;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .products-count {
        text-align: center;
    }

    .count-number {
        display: block;
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
    }

    .count-label {
        color: #666;
        font-size: 0.8rem;
    }

    .date-info {
        display: flex;
        flex-direction: column;
    }

    .date {
        font-weight: 500;
        color: #333;
    }

    .time {
        color: #666;
        font-size: 0.8rem;
    }

    @media (max-width: 768px) {
        .header-actions {
            flex-direction: column;
            width: 100%;
        }

        .header-actions .btn {
            width: 100%;
        }
    }
</style>
@endpush
@endsection
