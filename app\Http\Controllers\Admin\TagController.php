<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TagController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display a listing of tags.
     */
    public function index(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to tag management.');
        }

        $query = Tag::withCount('productGroups');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where('name', 'like', "%{$search}%");
        }

        $tags = $query->latest()->paginate(30);

        return view('admin.tags.index', compact('tags', 'admin'));
    }

    /**
     * Show the form for creating a new tag.
     */
    public function create()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to create tags.');
        }

        return view('admin.tags.create', compact('admin'));
    }

    /**
     * Store a newly created tag in storage.
     */
    public function store(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to create tags.');
        }

        $request->validate([
            'name' => 'required|string|max:100|unique:tags,name',
        ]);

        Tag::create([
            'name' => $request->name,
        ]);

        return redirect()->route('admin.tags.index')
            ->with('success', 'Tag created successfully!');
    }

    /**
     * Display the specified tag.
     */
    public function show(Tag $tag)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to view tags.');
        }

        $tag->load(['productGroups' => function($query) {
            $query->latest()->take(10);
        }]);

        return view('admin.tags.show', compact('tag', 'admin'));
    }

    /**
     * Show the form for editing the specified tag.
     */
    public function edit(Tag $tag)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to edit tags.');
        }

        return view('admin.tags.edit', compact('tag', 'admin'));
    }

    /**
     * Update the specified tag in storage.
     */
    public function update(Request $request, Tag $tag)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to update tags.');
        }

        $request->validate([
            'name' => 'required|string|max:100|unique:tags,name,' . $tag->id,
        ]);

        $tag->update([
            'name' => $request->name,
        ]);

        return redirect()->route('admin.tags.index')
            ->with('success', 'Tag updated successfully!');
    }

    /**
     * Remove the specified tag from storage.
     */
    public function destroy(Tag $tag)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            abort(403, 'Unauthorized access to delete tags.');
        }

        $tagName = $tag->name;
        
        // Detach from all products before deleting
        $tag->productGroups()->detach();
        $tag->delete();

        return redirect()->route('admin.tags.index')
            ->with('success', "Tag '{$tagName}' deleted successfully!");
    }

    /**
     * Bulk create tags.
     */
    public function bulkCreate(Request $request)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_tags')) {
            abort(403, 'Unauthorized access to create tags.');
        }

        $request->validate([
            'tags' => 'required|string',
        ]);

        $tagNames = array_filter(array_map('trim', explode(',', $request->tags)));
        $created = 0;
        $skipped = 0;

        foreach ($tagNames as $tagName) {
            if (strlen($tagName) > 0 && strlen($tagName) <= 100) {
                $exists = Tag::where('name', $tagName)->exists();
                if (!$exists) {
                    Tag::create(['name' => $tagName]);
                    $created++;
                } else {
                    $skipped++;
                }
            }
        }

        $message = "Created {$created} new tags.";
        if ($skipped > 0) {
            $message .= " Skipped {$skipped} existing tags.";
        }

        return redirect()->route('admin.tags.index')
            ->with('success', $message);
    }

    /**
     * Get tag statistics.
     */
    public function stats()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('view_analytics')) {
            abort(403, 'Unauthorized access to tag analytics.');
        }

        $stats = [
            'total_tags' => Tag::count(),
            'tags_with_products' => Tag::has('productGroups')->count(),
            'unused_tags' => Tag::doesntHave('productGroups')->count(),
        ];

        $popularTags = Tag::withCount('productGroups')
            ->orderBy('product_groups_count', 'desc')
            ->take(15)
            ->get();

        return view('admin.tags.stats', compact('stats', 'popularTags', 'admin'));
    }

    /**
     * Clean unused tags.
     */
    public function cleanUnused()
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            abort(403, 'Unauthorized access to delete tags.');
        }

        $deletedCount = Tag::doesntHave('productGroups')->delete();

        return redirect()->route('admin.tags.index')
            ->with('success', "Cleaned {$deletedCount} unused tags!");
    }
}
