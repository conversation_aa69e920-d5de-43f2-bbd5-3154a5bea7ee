<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CartItem;
use App\Models\User;
use App\Models\Product;

class CartSeeder extends Seeder
{
    public function run(): void
    {
        // Check if we have users and products
        $user = User::first();
        if (!$user) {
            $this->command->warn('No users found. Please run UsersSeeder first.');
            return;
        }

        $products = Product::inRandomOrder()->take(3)->get();
        if ($products->isEmpty()) {
            $this->command->warn('No products found. Please run ProductGroupSeeder first.');
            return;
        }

        foreach ($products as $product) {
            CartItem::create([
                'user_id' => $user->id,
                'product_variant_id' => $product->id, // This references our Product model (variants)
                'quantity' => rand(1, 3)
            ]);
        }

        $this->command->info('Cart items created successfully!');
    }
}
