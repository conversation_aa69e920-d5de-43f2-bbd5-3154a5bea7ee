<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductGroup;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProductVariantController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:admin');
    }

    /**
     * Display variants for a product group.
     */
    public function index(ProductGroup $productGroup)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to product variants.');
        }

        $variants = $productGroup->products()->paginate(20);

        return view('admin.products.variants.index', compact('productGroup', 'variants', 'admin'));
    }

    /**
     * Show the form for creating a new variant.
     */
    public function create(ProductGroup $productGroup)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to create variants.');
        }

        return view('admin.products.variants.create', compact('productGroup', 'admin'));
    }

    /**
     * Store a newly created variant.
     */
    public function store(Request $request, ProductGroup $productGroup)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to create variants.');
        }

        $request->validate([
            'color' => 'required|string|max:100',
            'size' => 'required|string|max:50',
            'price_adjustment' => 'required|numeric',
            'quantity' => 'required|integer|min:0',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'image' => 'nullable|string|max:255',
            'is_available' => 'boolean',
        ]);

        // Generate SKU if not provided
        $sku = $request->sku ?: $this->generateSKU($productGroup, $request->color, $request->size);

        $variant = $productGroup->products()->create([
            'color' => $request->color,
            'size' => $request->size,
            'price_adjustment' => $request->price_adjustment,
            'quantity' => $request->quantity,
            'sku' => $sku,
            'image' => $request->image,
            'is_available' => $request->boolean('is_available', true),
        ]);

        return redirect()->route('admin.products.variants.index', $productGroup)
            ->with('success', 'Product variant created successfully!');
    }

    /**
     * Show the form for editing a variant.
     */
    public function edit(ProductGroup $productGroup, Product $variant)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to edit variants.');
        }

        return view('admin.products.variants.edit', compact('productGroup', 'variant', 'admin'));
    }

    /**
     * Update the specified variant.
     */
    public function update(Request $request, ProductGroup $productGroup, Product $variant)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to update variants.');
        }

        $request->validate([
            'color' => 'required|string|max:100',
            'size' => 'required|string|max:50',
            'price_adjustment' => 'required|numeric',
            'quantity' => 'required|integer|min:0',
            'sku' => 'nullable|string|max:100|unique:products,sku,' . $variant->id,
            'image' => 'nullable|string|max:255',
            'is_available' => 'boolean',
        ]);

        $variant->update([
            'color' => $request->color,
            'size' => $request->size,
            'price_adjustment' => $request->price_adjustment,
            'quantity' => $request->quantity,
            'sku' => $request->sku ?: $variant->sku,
            'image' => $request->image,
            'is_available' => $request->boolean('is_available', true),
        ]);

        return redirect()->route('admin.products.variants.index', $productGroup)
            ->with('success', 'Product variant updated successfully!');
    }

    /**
     * Remove the specified variant.
     */
    public function destroy(ProductGroup $productGroup, Product $variant)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('delete_data')) {
            abort(403, 'Unauthorized access to delete variants.');
        }

        $variantInfo = "{$variant->color} - {$variant->size}";
        $variant->delete();

        return redirect()->route('admin.products.variants.index', $productGroup)
            ->with('success', "Variant '{$variantInfo}' deleted successfully!");
    }

    /**
     * Toggle variant availability.
     */
    public function toggleAvailability(ProductGroup $productGroup, Product $variant)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to modify variants.');
        }

        $variant->update(['is_available' => !$variant->is_available]);

        $status = $variant->is_available ? 'made available' : 'made unavailable';
        
        return redirect()->back()
            ->with('success', "Variant '{$variant->color} - {$variant->size}' has been {$status}!");
    }

    /**
     * Bulk update stock quantities.
     */
    public function bulkUpdateStock(Request $request, ProductGroup $productGroup)
    {
        $admin = Auth::guard('admin')->user();
        
        if (!$admin->hasPermission('manage_products')) {
            abort(403, 'Unauthorized access to update stock.');
        }

        $request->validate([
            'variants' => 'required|array',
            'variants.*.id' => 'required|exists:products,id',
            'variants.*.quantity' => 'required|integer|min:0',
        ]);

        $updated = 0;
        foreach ($request->variants as $variantData) {
            $variant = Product::find($variantData['id']);
            if ($variant && $variant->product_group_id == $productGroup->id) {
                $variant->update(['quantity' => $variantData['quantity']]);
                $updated++;
            }
        }

        return redirect()->back()
            ->with('success', "Updated stock for {$updated} variants successfully!");
    }

    /**
     * Generate SKU for variant.
     */
    private function generateSKU(ProductGroup $productGroup, string $color, string $size): string
    {
        $productCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $productGroup->name), 0, 3));
        $colorCode = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $color), 0, 2));
        $sizeCode = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $size));
        $timestamp = substr(time(), -4);
        
        return "{$productCode}-{$colorCode}-{$sizeCode}-{$timestamp}";
    }
}
