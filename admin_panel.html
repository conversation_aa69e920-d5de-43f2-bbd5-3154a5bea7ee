<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Product Management Admin Panel</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                background-color: #f8f9fa;
                color: #333;
            }

            .header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1rem 2rem;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .header h1 {
                font-size: 1.8rem;
                font-weight: 300;
            }

            .container {
                max-width: 1400px;
                margin: 2rem auto;
                padding: 0 1rem;
            }

            .nav-tabs {
                display: flex;
                background: white;
                border-radius: 8px 8px 0 0;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                overflow: hidden;
            }

            .nav-tab {
                flex: 1;
                padding: 1rem;
                text-align: center;
                background: #f8f9fa;
                border: none;
                cursor: pointer;
                font-size: 1rem;
                transition: all 0.3s ease;
            }

            .nav-tab.active {
                background: white;
                color: #667eea;
                font-weight: 600;
            }

            .nav-tab:hover {
                background: #e9ecef;
            }

            .tab-content {
                background: white;
                border-radius: 0 0 8px 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                min-height: 600px;
            }

            .tab-pane {
                display: none;
                padding: 2rem;
            }

            .tab-pane.active {
                display: block;
            }

            .card {
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                margin-bottom: 1.5rem;
                overflow: hidden;
            }

            .card-header {
                background: #f8f9fa;
                padding: 1rem 1.5rem;
                border-bottom: 1px solid #dee2e6;
                font-weight: 600;
                color: #495057;
            }

            .card-body {
                padding: 1.5rem;
            }

            .form-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-group label {
                display: block;
                margin-bottom: 0.5rem;
                font-weight: 500;
                color: #495057;
            }

            .form-control {
                width: 100%;
                padding: 0.75rem;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 1rem;
                transition: border-color 0.15s ease-in-out,
                    box-shadow 0.15s ease-in-out;
            }

            .form-control:focus {
                outline: none;
                border-color: #667eea;
                box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            }

            .btn {
                display: inline-block;
                padding: 0.75rem 1.5rem;
                margin: 0.25rem;
                border: none;
                border-radius: 4px;
                font-size: 1rem;
                font-weight: 500;
                text-align: center;
                cursor: pointer;
                transition: all 0.15s ease-in-out;
                text-decoration: none;
            }

            .btn-primary {
                background-color: #667eea;
                color: white;
            }

            .btn-primary:hover {
                background-color: #5a6fd8;
                transform: translateY(-1px);
            }

            .btn-success {
                background-color: #28a745;
                color: white;
            }

            .btn-success:hover {
                background-color: #218838;
                transform: translateY(-1px);
            }

            .btn-danger {
                background-color: #dc3545;
                color: white;
            }

            .btn-danger:hover {
                background-color: #c82333;
                transform: translateY(-1px);
            }

            .btn-secondary {
                background-color: #6c757d;
                color: white;
            }

            .btn-secondary:hover {
                background-color: #5a6268;
                transform: translateY(-1px);
            }

            .table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 1rem;
            }

            .table th,
            .table td {
                padding: 0.75rem;
                text-align: left;
                border-bottom: 1px solid #dee2e6;
            }

            .table th {
                background-color: #f8f9fa;
                font-weight: 600;
                color: #495057;
            }

            .table tbody tr:hover {
                background-color: #f8f9fa;
            }

            .badge {
                display: inline-block;
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                font-weight: 700;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
                border-radius: 0.25rem;
            }

            .badge-success {
                color: #fff;
                background-color: #28a745;
            }

            .badge-danger {
                color: #fff;
                background-color: #dc3545;
            }

            .badge-warning {
                color: #212529;
                background-color: #ffc107;
            }

            .alert {
                padding: 0.75rem 1.25rem;
                margin-bottom: 1rem;
                border: 1px solid transparent;
                border-radius: 0.25rem;
            }

            .alert-success {
                color: #155724;
                background-color: #d4edda;
                border-color: #c3e6cb;
            }

            .alert-danger {
                color: #721c24;
                background-color: #f8d7da;
                border-color: #f5c6cb;
            }

            .variant-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1rem;
                margin-top: 1rem;
            }

            .variant-card {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 1rem;
                background: #f8f9fa;
            }

            .variant-card h5 {
                margin-bottom: 0.5rem;
                color: #495057;
            }

            .loading {
                text-align: center;
                padding: 2rem;
                color: #6c757d;
            }

            .spinner {
                border: 4px solid #f3f3f3;
                border-top: 4px solid #667eea;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 1s linear infinite;
                margin: 0 auto;
            }

            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 1.5rem;
                border-radius: 8px;
                text-align: center;
            }

            .stat-number {
                font-size: 2rem;
                font-weight: bold;
                margin-bottom: 0.5rem;
            }

            .stat-label {
                font-size: 0.9rem;
                opacity: 0.9;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🛍️ Product Management Admin Panel</h1>
        </div>

        <div class="container">
            <!-- Navigation Tabs -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('dashboard')">
                    Dashboard
                </button>
                <button class="nav-tab" onclick="showTab('product-groups')">
                    Product Groups
                </button>

                <button class="nav-tab" onclick="showTab('categories')">
                    Categories
                </button>
                <button class="nav-tab" onclick="showTab('tags')">Tags</button>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Dashboard Tab -->
                <div id="dashboard" class="tab-pane active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalProductGroups">
                                0
                            </div>
                            <div class="stat-label">Product Groups</div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-number" id="totalCategories">
                                0
                            </div>
                            <div class="stat-label">Categories</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalTags">0</div>
                            <div class="stat-label">Tags</div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">Recent Activity</div>
                        <div class="card-body">
                            <p>
                                Welcome to the Product Management Admin Panel!
                            </p>
                            <p>
                                Use the tabs above to manage your product
                                catalog:
                            </p>
                            <ul>
                                <li>
                                    <strong>Product Groups:</strong> Manage main
                                    products with their base information and all
                                    color/size variants
                                </li>

                                <li>
                                    <strong>Categories:</strong> Organize
                                    products into categories
                                </li>
                                <li>
                                    <strong>Tags:</strong> Add tags for better
                                    product discovery and similar product
                                    recommendations
                                </li>
                            </ul>
                            <hr />
                            <p><strong>New Simplified Structure:</strong></p>
                            <ul>
                                <li>
                                    Each Product Group contains multiple
                                    variants (color + size combinations)
                                </li>
                                <li>
                                    Colors and sizes are stored directly as text
                                    (no separate tables)
                                </li>
                                <li>
                                    Each variant can have its own price
                                    adjustment and stock quantity
                                </li>
                                <li>Much simpler and easier to manage!</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Product Groups Tab -->
                <div id="product-groups" class="tab-pane">
                    <div class="card">
                        <div class="card-header">
                            Create New Product Group
                            <button
                                class="btn btn-primary"
                                style="float: right"
                                onclick="toggleCreateForm()"
                            >
                                + New Product Group
                            </button>
                        </div>
                        <div
                            class="card-body"
                            id="createProductGroupForm"
                            style="display: none"
                        >
                            <!-- Create form will be loaded here -->
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">Product Groups</div>
                        <div class="card-body">
                            <div id="productGroupsList">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <p>Loading product groups...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories Tab -->
                <div id="categories" class="tab-pane">
                    <div class="card">
                        <div class="card-header">
                            Categories
                            <button
                                class="btn btn-primary"
                                style="float: right"
                                onclick="showCreateCategoryForm()"
                            >
                                + New Category
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="createCategoryForm" style="display: none">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label>Category Name</label>
                                        <input
                                            type="text"
                                            class="form-control"
                                            id="categoryName"
                                            placeholder="Enter category name"
                                        />
                                    </div>
                                    <div class="form-group">
                                        <label>Description</label>
                                        <textarea
                                            class="form-control"
                                            id="categoryDescription"
                                            placeholder="Enter category description"
                                        ></textarea>
                                    </div>
                                </div>
                                <button
                                    class="btn btn-success"
                                    onclick="createCategory()"
                                >
                                    Create Category
                                </button>
                                <button
                                    class="btn btn-secondary"
                                    onclick="hideCreateCategoryForm()"
                                >
                                    Cancel
                                </button>
                            </div>
                            <div id="categoriesList">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <p>Loading categories...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tags Tab -->
                <div id="tags" class="tab-pane">
                    <div class="card">
                        <div class="card-header">
                            Tags
                            <button
                                class="btn btn-primary"
                                style="float: right"
                                onclick="showCreateTagForm()"
                            >
                                + New Tag
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="createTagForm" style="display: none">
                                <div class="form-group">
                                    <label>Tag Name</label>
                                    <input
                                        type="text"
                                        class="form-control"
                                        id="tagName"
                                        placeholder="Enter tag name"
                                    />
                                </div>
                                <button
                                    class="btn btn-success"
                                    onclick="createTag()"
                                >
                                    Create Tag
                                </button>
                                <button
                                    class="btn btn-secondary"
                                    onclick="hideCreateTagForm()"
                                >
                                    Cancel
                                </button>
                            </div>
                            <div id="tagsList">
                                <div class="loading">
                                    <div class="spinner"></div>
                                    <p>Loading tags...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div
            id="alertContainer"
            style="position: fixed; top: 20px; right: 20px; z-index: 1000"
        ></div>

        <script>
            const API_BASE = "http://localhost:8000/api";
            let formData = {};

            // Tab Management
            function showTab(tabName) {
                // Hide all tabs
                document.querySelectorAll(".tab-pane").forEach((pane) => {
                    pane.classList.remove("active");
                });
                document.querySelectorAll(".nav-tab").forEach((tab) => {
                    tab.classList.remove("active");
                });

                // Show selected tab
                document.getElementById(tabName).classList.add("active");
                event.target.classList.add("active");

                // Load data for the tab
                loadTabData(tabName);
            }

            // Load data based on active tab
            function loadTabData(tabName) {
                switch (tabName) {
                    case "dashboard":
                        loadDashboardStats();
                        break;
                    case "product-groups":
                        loadProductGroups();
                        break;

                    case "categories":
                        loadCategories();
                        break;
                    case "tags":
                        loadTags();
                        break;
                }
            }

            // API Helper Functions
            async function makeRequest(url, method = "GET", data = null) {
                try {
                    const options = {
                        method: method,
                        headers: {
                            "Content-Type": "application/json",
                            Accept: "application/json",
                        },
                    };

                    if (data) {
                        options.body = JSON.stringify(data);
                    }

                    const response = await fetch(url, options);
                    const result = await response.json();

                    return {
                        success: response.ok,
                        status: response.status,
                        data: result,
                    };
                } catch (error) {
                    return {
                        success: false,
                        error: error.message,
                    };
                }
            }

            // Alert Functions
            function showAlert(message, type = "success") {
                const alertContainer =
                    document.getElementById("alertContainer");
                const alert = document.createElement("div");
                alert.className = `alert alert-${type}`;
                alert.textContent = message;
                alert.style.marginBottom = "10px";

                alertContainer.appendChild(alert);

                setTimeout(() => {
                    alert.remove();
                }, 5000);
            }

            // Dashboard Functions
            async function loadDashboardStats() {
                try {
                    const [productGroups, categories, tags] = await Promise.all(
                        [
                            makeRequest(`${API_BASE}/product-groups`),
                            makeRequest(`${API_BASE}/categories`),
                            makeRequest(`${API_BASE}/tags`),
                        ]
                    );

                    document.getElementById("totalProductGroups").textContent =
                        productGroups.success
                            ? productGroups.data.data
                                ? productGroups.data.data.length
                                : productGroups.data.length
                            : 0;

                    document.getElementById("totalCategories").textContent =
                        categories.success ? categories.data.length : 0;
                    document.getElementById("totalTags").textContent =
                        tags.success ? tags.data.length : 0;
                } catch (error) {
                    console.error("Error loading dashboard stats:", error);
                }
            }

            // Product Groups Functions
            async function loadProductGroups() {
                const container = document.getElementById("productGroupsList");
                container.innerHTML =
                    '<div class="loading"><div class="spinner"></div><p>Loading product groups...</p></div>';

                const result = await makeRequest(`${API_BASE}/product-groups`);

                if (result.success) {
                    const productGroups = result.data.data || result.data;
                    if (productGroups.length === 0) {
                        container.innerHTML =
                            "<p>No product groups found. Create your first product group!</p>";
                    } else {
                        container.innerHTML =
                            generateProductGroupsTable(productGroups);
                    }
                } else {
                    container.innerHTML =
                        '<div class="alert alert-danger">Error loading product groups</div>';
                }
            }

            function generateProductGroupsTable(productGroups) {
                let html = '<table class="table"><thead><tr>';
                html +=
                    "<th>Name</th><th>Category</th><th>Base Price</th><th>Status</th><th>Actions</th>";
                html += "</tr></thead><tbody>";

                productGroups.forEach((group) => {
                    html += "<tr>";
                    html += `<td>${group.name}</td>`;
                    html += `<td>${
                        group.category ? group.category.name : "N/A"
                    }</td>`;
                    html += `<td>$${group.base_price}</td>`;
                    html += `<td><span class="badge ${
                        group.is_active ? "badge-success" : "badge-danger"
                    }">${group.is_active ? "Active" : "Inactive"}</span></td>`;
                    html += `<td>
                    <button class="btn btn-primary" onclick="viewProductGroup(${group.id})">View</button>
                    <button class="btn btn-danger" onclick="deleteProductGroup(${group.id})">Delete</button>
                </td>`;
                    html += "</tr>";
                });

                html += "</tbody></table>";
                return html;
            }

            // Category Functions
            async function loadCategories() {
                const container = document.getElementById("categoriesList");
                container.innerHTML =
                    '<div class="loading"><div class="spinner"></div><p>Loading categories...</p></div>';

                const result = await makeRequest(`${API_BASE}/categories`);

                if (result.success) {
                    if (result.data.length === 0) {
                        container.innerHTML =
                            "<p>No categories found. Create your first category!</p>";
                    } else {
                        container.innerHTML = generateCategoriesTable(
                            result.data
                        );
                    }
                } else {
                    container.innerHTML =
                        '<div class="alert alert-danger">Error loading categories</div>';
                }
            }

            function generateCategoriesTable(categories) {
                let html = '<table class="table"><thead><tr>';
                html += "<th>Name</th><th>Description</th><th>Actions</th>";
                html += "</tr></thead><tbody>";

                categories.forEach((category) => {
                    html += "<tr>";
                    html += `<td>${category.name}</td>`;
                    html += `<td>${category.description || "N/A"}</td>`;
                    html += `<td>
                    <button class="btn btn-danger" onclick="deleteCategory(${category.id})">Delete</button>
                </td>`;
                    html += "</tr>";
                });

                html += "</tbody></table>";
                return html;
            }

            function showCreateCategoryForm() {
                document.getElementById("createCategoryForm").style.display =
                    "block";
            }

            function hideCreateCategoryForm() {
                document.getElementById("createCategoryForm").style.display =
                    "none";
                document.getElementById("categoryName").value = "";
                document.getElementById("categoryDescription").value = "";
            }

            async function createCategory() {
                const name = document.getElementById("categoryName").value;
                const description = document.getElementById(
                    "categoryDescription"
                ).value;

                if (!name) {
                    showAlert("Please enter a category name", "danger");
                    return;
                }

                const result = await makeRequest(
                    `${API_BASE}/categories`,
                    "POST",
                    {
                        name: name,
                        description: description,
                    }
                );

                if (result.success) {
                    showAlert("Category created successfully!");
                    hideCreateCategoryForm();
                    loadCategories();
                } else {
                    showAlert("Error creating category", "danger");
                }
            }

            async function deleteCategory(id) {
                if (!confirm("Are you sure you want to delete this category?"))
                    return;

                const result = await makeRequest(
                    `${API_BASE}/categories/${id}`,
                    "DELETE"
                );

                if (result.success) {
                    showAlert("Category deleted successfully!");
                    loadCategories();
                } else {
                    showAlert("Error deleting category", "danger");
                }
            }

            // Tag Functions
            async function loadTags() {
                const container = document.getElementById("tagsList");
                container.innerHTML =
                    '<div class="loading"><div class="spinner"></div><p>Loading tags...</p></div>';

                const result = await makeRequest(`${API_BASE}/tags`);

                if (result.success) {
                    if (result.data.length === 0) {
                        container.innerHTML =
                            "<p>No tags found. Create your first tag!</p>";
                    } else {
                        container.innerHTML = generateTagsTable(result.data);
                    }
                } else {
                    container.innerHTML =
                        '<div class="alert alert-danger">Error loading tags</div>';
                }
            }

            function generateTagsTable(tags) {
                let html = '<table class="table"><thead><tr>';
                html += "<th>Name</th><th>Actions</th>";
                html += "</tr></thead><tbody>";

                tags.forEach((tag) => {
                    html += "<tr>";
                    html += `<td>${tag.name}</td>`;
                    html += `<td>
                    <button class="btn btn-danger" onclick="deleteTag(${tag.id})">Delete</button>
                </td>`;
                    html += "</tr>";
                });

                html += "</tbody></table>";
                return html;
            }

            function showCreateTagForm() {
                document.getElementById("createTagForm").style.display =
                    "block";
            }

            function hideCreateTagForm() {
                document.getElementById("createTagForm").style.display = "none";
                document.getElementById("tagName").value = "";
            }

            async function createTag() {
                const name = document.getElementById("tagName").value;

                if (!name) {
                    showAlert("Please enter a tag name", "danger");
                    return;
                }

                const result = await makeRequest(`${API_BASE}/tags`, "POST", {
                    name: name,
                });

                if (result.success) {
                    showAlert("Tag created successfully!");
                    hideCreateTagForm();
                    loadTags();
                } else {
                    showAlert("Error creating tag", "danger");
                }
            }

            async function deleteTag(id) {
                if (!confirm("Are you sure you want to delete this tag?"))
                    return;

                const result = await makeRequest(
                    `${API_BASE}/tags/${id}`,
                    "DELETE"
                );

                if (result.success) {
                    showAlert("Tag deleted successfully!");
                    loadTags();
                } else {
                    showAlert("Error deleting tag", "danger");
                }
            }

            // Product Group Functions
            async function viewProductGroup(id) {
                const result = await makeRequest(
                    `${API_BASE}/product-groups/${id}`
                );

                if (result.success) {
                    alert(
                        `Product Group: ${
                            result.data.product_group.name
                        }\nVariants: ${
                            result.data.product_group.products
                                ? result.data.product_group.products.length
                                : 0
                        }`
                    );
                }
            }

            async function deleteProductGroup(id) {
                if (
                    !confirm(
                        "Are you sure you want to delete this product group and all its variants?"
                    )
                )
                    return;

                const result = await makeRequest(
                    `${API_BASE}/product-groups/${id}`,
                    "DELETE"
                );

                if (result.success) {
                    showAlert("Product group deleted successfully!");
                    loadProductGroups();
                } else {
                    showAlert("Error deleting product group", "danger");
                }
            }

            function toggleCreateForm() {
                const form = document.getElementById("createProductGroupForm");
                if (form.style.display === "none") {
                    form.style.display = "block";
                    loadCreateProductGroupForm();
                } else {
                    form.style.display = "none";
                }
            }

            async function loadCreateProductGroupForm() {
                const container = document.getElementById(
                    "createProductGroupForm"
                );

                // Load form data
                const result = await makeRequest(
                    `${API_BASE}/product-groups/form-data`
                );

                if (result.success) {
                    formData = result.data;
                    container.innerHTML = generateCreateProductGroupForm();
                } else {
                    container.innerHTML =
                        '<div class="alert alert-danger">Error loading form data</div>';
                }
            }

            function generateCreateProductGroupForm() {
                let html = '<div class="form-grid">';

                html += '<div class="form-group">';
                html += "<label>Product Name</label>";
                html +=
                    '<input type="text" class="form-control" id="productName" placeholder="Enter product name">';
                html += "</div>";

                html += '<div class="form-group">';
                html += "<label>Category</label>";
                html += '<select class="form-control" id="categoryId">';
                html += '<option value="">Select Category</option>';
                formData.categories.forEach((category) => {
                    html += `<option value="${category.id}">${category.name}</option>`;
                });
                html += "</select>";
                html += "</div>";

                html += '<div class="form-group">';
                html += "<label>Base Price ($)</label>";
                html +=
                    '<input type="number" class="form-control" id="basePrice" step="0.01" placeholder="0.00">';
                html += "</div>";

                html += '<div class="form-group">';
                html += "<label>Description</label>";
                html +=
                    '<textarea class="form-control" id="productDescription" placeholder="Enter product description"></textarea>';
                html += "</div>";

                html += "</div>";

                html += '<div class="form-group">';
                html += "<label>Tags</label>";
                html +=
                    '<select class="form-control" id="productTags" multiple>';
                formData.tags.forEach((tag) => {
                    html += `<option value="${tag.id}">${tag.name}</option>`;
                });
                html += "</select>";
                html += "</div>";

                html += "<h5>Product Variants</h5>";
                html += '<div id="variantsContainer">';
                html += '<div class="variant-card">';
                html += '<div class="form-grid">';
                html += '<div class="form-group">';
                html += "<label>Color</label>";
                html +=
                    '<input type="text" class="form-control variant-color" placeholder="e.g., Red">';
                html += "</div>";
                html += '<div class="form-group">';
                html += "<label>Size</label>";
                html +=
                    '<input type="text" class="form-control variant-size" placeholder="e.g., M">';
                html += "</div>";
                html += '<div class="form-group">';
                html += "<label>Price Adjustment ($)</label>";
                html +=
                    '<input type="number" class="form-control variant-price-adjustment" step="0.01" value="0">';
                html += "</div>";
                html += '<div class="form-group">';
                html += "<label>Quantity</label>";
                html +=
                    '<input type="number" class="form-control variant-quantity" value="0">';
                html += "</div>";
                html += "</div>";
                html += "</div>";
                html += "</div>";

                html +=
                    '<button type="button" class="btn btn-secondary" onclick="addVariant()">+ Add Variant</button>';
                html +=
                    '<button type="button" class="btn btn-success" onclick="createProductGroup()">Create Product Group</button>';
                html +=
                    '<button type="button" class="btn btn-secondary" onclick="toggleCreateForm()">Cancel</button>';

                return html;
            }

            function addVariant() {
                const container = document.getElementById("variantsContainer");
                const variantHtml = `
                <div class="variant-card">
                    <div class="form-grid">
                        <div class="form-group">
                            <label>Color</label>
                            <input type="text" class="form-control variant-color" placeholder="e.g., Blue">
                        </div>
                        <div class="form-group">
                            <label>Size</label>
                            <input type="text" class="form-control variant-size" placeholder="e.g., L">
                        </div>
                        <div class="form-group">
                            <label>Price Adjustment ($)</label>
                            <input type="number" class="form-control variant-price-adjustment" step="0.01" value="0">
                        </div>
                        <div class="form-group">
                            <label>Quantity</label>
                            <input type="number" class="form-control variant-quantity" value="0">
                        </div>
                    </div>
                    <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">Remove</button>
                </div>
            `;
                container.insertAdjacentHTML("beforeend", variantHtml);
            }

            async function createProductGroup() {
                const name = document.getElementById("productName").value;
                const categoryId = document.getElementById("categoryId").value;
                const basePrice = document.getElementById("basePrice").value;
                const description =
                    document.getElementById("productDescription").value;
                const tags = Array.from(
                    document.getElementById("productTags").selectedOptions
                ).map((option) => parseInt(option.value));

                if (!name || !categoryId || !basePrice) {
                    showAlert("Please fill in all required fields", "danger");
                    return;
                }

                // Collect variants
                const variants = [];
                document.querySelectorAll(".variant-card").forEach((card) => {
                    const color = card.querySelector(".variant-color").value;
                    const size = card.querySelector(".variant-size").value;
                    const priceAdjustment = card.querySelector(
                        ".variant-price-adjustment"
                    ).value;
                    const quantity =
                        card.querySelector(".variant-quantity").value;

                    if (color && size) {
                        variants.push({
                            color: color,
                            size: size,
                            price_adjustment: parseFloat(priceAdjustment) || 0,
                            quantity: parseInt(quantity) || 0,
                        });
                    }
                });

                if (variants.length === 0) {
                    showAlert("Please add at least one variant", "danger");
                    return;
                }

                const data = {
                    name: name,
                    description: description,
                    category_id: parseInt(categoryId),
                    base_price: parseFloat(basePrice),
                    is_active: true,
                    is_trending: false,
                    rating: 0,
                    tags: tags,
                    variants: variants,
                };

                const result = await makeRequest(
                    `${API_BASE}/product-groups`,
                    "POST",
                    data
                );

                if (result.success) {
                    showAlert("Product group created successfully!");
                    toggleCreateForm();
                    loadProductGroups();
                    loadDashboardStats();
                } else {
                    showAlert(
                        "Error creating product group: " +
                            (result.data.message || "Unknown error"),
                        "danger"
                    );
                }
            }

            // Initialize the page
            window.onload = () => {
                loadDashboardStats();
            };
        </script>
    </body>
</html>
