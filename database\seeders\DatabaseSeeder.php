<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting database seeding...');

        $this->call([
            // Core data (must run first)
            CategoriesSeeder::class,
            TagsSeeder::class,

            // User accounts
            UsersSeeder::class,
            AdminSeeder::class,

            // Products (depends on categories and tags)
            ProductGroupSeeder::class,

            // User interactions (depends on users and products)
            CartSeeder::class,
            WishlistSeeder::class,
        ]);

        $this->command->info('🎉 Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('📋 Summary:');
        $this->command->info('- Categories: 8 created');
        $this->command->info('- Tags: 50+ created');
        $this->command->info('- Users: 5 test accounts created');
        $this->command->info('- Admins: 4 admin accounts created');
        $this->command->info('- Product Groups: 5 with multiple variants');
        $this->command->info('- Cart & Wishlist: Sample data created');
        $this->command->info('');
        $this->command->info('🔑 Test Accounts:');
        $this->command->info('Users: <EMAIL>, <EMAIL>, <EMAIL>');
        $this->command->info('Admins: <EMAIL>, <EMAIL>');
        $this->command->info('Password: password123 (or <NAME_EMAIL>)');
    }
}
