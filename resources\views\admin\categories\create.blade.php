@extends('admin.layouts.app')

@section('title', 'Create Category')

@section('content')
<div class="admin-header">
    <div class="header-content">
        <div>
            <h1 class="page-title">➕ Create New Category</h1>
            <p class="page-subtitle">Add a new product category</p>
        </div>
        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline">
            ← Back to Categories
        </a>
    </div>
</div>

<div class="form-card">
    <form action="{{ route('admin.categories.store') }}" method="POST" class="category-form">
        @csrf
        
        <div class="form-section">
            <h3 class="section-title">📝 Category Information</h3>
            
            <div class="form-group">
                <label for="name" class="form-label">Category Name *</label>
                <input type="text" id="name" name="name" class="form-control @error('name') is-invalid @enderror" 
                       value="{{ old('name') }}" required placeholder="Enter category name">
                @error('name')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Choose a clear, descriptive name for this category.</small>
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <textarea id="description" name="description" rows="4" 
                          class="form-control @error('description') is-invalid @enderror" 
                          placeholder="Enter category description (optional)">{{ old('description') }}</textarea>
                @error('description')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
                <small class="form-text">Provide a brief description of what products belong in this category.</small>
            </div>
        </div>

        <div class="form-section">
            <h3 class="section-title">⚙️ Settings</h3>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="is_active" value="1" 
                           {{ old('is_active', true) ? 'checked' : '' }}>
                    <span class="checkmark"></span>
                    Active Category
                </label>
                <small class="form-text">Active categories are visible to customers and can be used for products.</small>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                ✅ Create Category
            </button>
            <a href="{{ route('admin.categories.index') }}" class="btn btn-outline">
                Cancel
            </a>
        </div>
    </form>
</div>

<!-- Category Guidelines -->
<div class="guidelines-card">
    <h3 class="guidelines-title">📋 Category Guidelines</h3>
    <div class="guidelines-content">
        <div class="guideline-section">
            <h4>✅ Good Category Names</h4>
            <ul>
                <li>Electronics</li>
                <li>Clothing & Apparel</li>
                <li>Home & Garden</li>
                <li>Sports & Outdoors</li>
                <li>Books & Media</li>
            </ul>
        </div>
        
        <div class="guideline-section">
            <h4>❌ Avoid These</h4>
            <ul>
                <li>Too generic names (e.g., "Stuff")</li>
                <li>Overly specific names (e.g., "Red Nike Shoes Size 10")</li>
                <li>Special characters or numbers</li>
                <li>Duplicate or similar names</li>
            </ul>
        </div>
        
        <div class="guideline-section">
            <h4>💡 Best Practices</h4>
            <ul>
                <li>Use clear, descriptive names</li>
                <li>Keep names concise but informative</li>
                <li>Think about customer browsing habits</li>
                <li>Consider future product additions</li>
                <li>Use consistent naming conventions</li>
            </ul>
        </div>
    </div>
</div>

@push('styles')
<style>
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .category-form {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid #e9ecef;
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 0.875rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-control.is-invalid {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .form-text {
        color: #666;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        font-weight: 500;
        color: #333;
    }

    .checkbox-label input[type="checkbox"] {
        width: 1.2rem;
        height: 1.2rem;
        margin: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
    }

    .guidelines-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 2rem;
    }

    .guidelines-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .guidelines-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .guideline-section h4 {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #333;
    }

    .guideline-section ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .guideline-section li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
        color: #666;
    }

    .guideline-section li:last-child {
        border-bottom: none;
    }

    @media (max-width: 768px) {
        .form-actions {
            flex-direction: column;
        }

        .guidelines-content {
            grid-template-columns: 1fr;
        }
    }
</style>
@endpush
@endsection
