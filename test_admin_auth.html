<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Authentication Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: white;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-section h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }

        .test-section p {
            margin-bottom: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: white;
            color: #667eea;
        }

        .btn-primary:hover {
            background: #f8f9fa;
        }

        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .account-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .account-card h4 {
            margin-bottom: 0.5rem;
            color: #fff;
        }

        .account-card p {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .permissions-list {
            list-style: none;
            padding: 0;
            margin: 1rem 0;
        }

        .permissions-list li {
            padding: 0.25rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .permissions-list li:last-child {
            border-bottom: none;
        }

        .permissions-list li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: "🔐 ";
            margin-right: 0.5rem;
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .container {
                padding: 1rem;
            }

            .accounts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">👨‍💼 Admin Authentication System</h1>
            <p class="subtitle">Role-based Admin Panel with Advanced Security</p>
        </div>

        <div class="test-section">
            <h3>🔑 Admin Login</h3>
            <p>Access the admin panel with role-based permissions and security features.</p>
            <a href="http://localhost:8000/admin/login" class="btn btn-primary">Admin Login Page</a>
            <a href="http://localhost:8000/admin/dashboard" class="btn">Admin Dashboard (Protected)</a>
        </div>

        <div class="test-section">
            <h3>👥 Test Admin Accounts</h3>
            <p>Use these pre-configured admin accounts to test different permission levels:</p>
            
            <div class="accounts-grid">
                <div class="account-card">
                    <h4>🔴 Super Administrator</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> password123</p>
                    <p><strong>Role:</strong> super_admin</p>
                    <p><strong>Access:</strong> All permissions</p>
                </div>

                <div class="account-card">
                    <h4>🔵 Store Manager</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> password123</p>
                    <p><strong>Role:</strong> admin</p>
                    <p><strong>Access:</strong> Product & order management</p>
                </div>

                <div class="account-card">
                    <h4>🟢 Content Moderator</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> password123</p>
                    <p><strong>Role:</strong> moderator</p>
                    <p><strong>Access:</strong> Limited management</p>
                </div>

                <div class="account-card">
                    <h4>🟡 Content Editor</h4>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Password:</strong> password123</p>
                    <p><strong>Role:</strong> editor</p>
                    <p><strong>Access:</strong> Basic editing</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🛡️ Security Features</h3>
            <ul class="feature-list">
                <li>Separate admin authentication guard</li>
                <li>Role-based permission system</li>
                <li>Account locking after failed attempts</li>
                <li>Login attempt tracking</li>
                <li>Last login timestamp and IP tracking</li>
                <li>Account status management (active/inactive)</li>
                <li>Session-based authentication with remember me</li>
                <li>Audit trail (created_by, updated_by)</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🎯 Permission System</h3>
            <p>Each admin role has specific permissions stored as JSON arrays:</p>
            
            <div class="code-block">
Super Admin Permissions:
- manage_users, manage_admins, manage_products
- manage_categories, manage_tags, manage_orders
- view_analytics, manage_settings, manage_permissions
- delete_data, export_data, import_data

Manager Permissions:
- manage_products, manage_categories, manage_tags
- manage_orders, view_analytics

Moderator Permissions:
- manage_products, manage_categories, view_analytics

Editor Permissions:
- manage_products, manage_categories
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Test Workflow</h3>
            <p>Follow this workflow to test the admin authentication system:</p>
            <div class="code-block">
1. Visit Admin Login → http://localhost:8000/admin/login
2. Try Super Admin → <EMAIL> / password123
3. Access Dashboard → Should show all statistics and options
4. Test Permissions → Different actions based on role
5. Logout → Should redirect to admin login
6. Try Different Roles → Test manager, moderator, editor
7. Test Security → Try wrong password (account locks after 5 attempts)
8. Test Regular User → Should not access admin panel
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Technical Implementation</h3>
            <ul class="feature-list">
                <li>Separate 'admin' authentication guard</li>
                <li>Admin model with Authenticatable trait</li>
                <li>Custom AdminAuthController with security logic</li>
                <li>Role-based middleware protection</li>
                <li>Blade views with permission checks</li>
                <li>Session management with admin guard</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🌐 Navigation</h3>
            <a href="http://localhost:8000/" class="btn">Main Website</a>
            <a href="http://localhost:8000/login" class="btn">User Login</a>
            <a href="http://localhost:8000/admin/login" class="btn btn-primary">Admin Login</a>
            <a href="http://localhost:8000/debug-login" class="btn">Debug User Login</a>
        </div>
    </div>
</body>
</html>
