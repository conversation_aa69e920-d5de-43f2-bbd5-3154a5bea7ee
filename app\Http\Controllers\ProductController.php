<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Http\Resources\ProductResource;

class ProductController extends Controller
{
    // عرض كل المنتجات مع pagination
    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 10); // عدد المنتجات في كل صفحة (افتراضي 10)
        $perPage = min($perPage, 50); // حد أقصى 50 منتج في الصفحة

        $query = Product::with(['tags', 'variants', 'category'])
                        ->where('is_active', true);

        // فلترة حسب الفئة
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // فلترة حسب المنتجات الرائجة
        if ($request->has('trending') && $request->trending) {
            $query->where('is_trending', true);
        }

        // البحث في اسم المنتج
        if ($request->has('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // ترتيب المنتجات
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['name', 'price', 'rating', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate($perPage);

        return ProductResource::collection($products);
    }

    // عرض المنتجات للصفحة الرئيسية (مبسط وسريع)
    public function homepage(Request $request)
    {
        $perPage = $request->get('per_page', 12); // 12 منتج للصفحة الرئيسية
        $perPage = min($perPage, 24); // حد أقصى 24 منتج

        $products = Product::select(['id', 'name', 'price', 'rating', 'is_trending'])
                          ->where('is_active', true)
                          ->orderBy('is_trending', 'desc')
                          ->orderBy('rating', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);

        return response()->json([
            'data' => $products->items(),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'has_more_pages' => $products->hasMorePages(),
            ]
        ]);
    }

    // عرض منتج معين
    public function show(Product $product)
    {
        return new ProductResource($product->load(['tags', 'variants', 'category']));
    }

    // إنشاء منتج جديد
    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'price' => 'required|numeric',
            'category_id' => 'required|exists:categories,id',
            'is_active' => 'boolean',
            'is_trending' => 'boolean',
            'rating' => 'numeric|min:0|max:5',
            'tags' => 'array',
        ]);

        $product = Product::create($data);

        if ($request->has('tags')) {
            $product->tags()->sync($request->tags);
        }

        return response()->json($product->load('tags'), 201);
    }

    // تعديل منتج
    public function update(Request $request, Product $product)
    {
        $data = $request->validate([
        'name' => 'sometimes|string',
        'description' => 'nullable|string',
        'price' => 'sometimes|numeric',
        'category_id' => 'sometimes|exists:categories,id',
        'is_active' => 'boolean',
        'is_trending' => 'boolean',
        'rating' => 'numeric|min:0|max:5',
        'tags' => 'array',
        ]);

        $product->update($data);

        if ($request->has('tags')) {
            $product->tags()->sync($request->tags);
        }

        return response()->json($product->load('tags'));
    }

    // حذف منتج
    public function destroy(Product $product)
    {
        $product->delete();
        return response()->json(null, 204);
    }
}
