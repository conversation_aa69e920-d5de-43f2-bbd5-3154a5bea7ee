<!DOCTYPE html>
<html>
<head>
    <title>Debug Login Test</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        input { padding: 12px; width: 100%; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        button { padding: 12px 24px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .accounts { background: #e2e3e5; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .accounts ul { margin: 10px 0; padding-left: 20px; }
        .accounts li { margin-bottom: 5px; }
        h1 { color: #333; text-align: center; }
        h3 { margin-top: 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Login Test</h1>
        
        <div class="accounts">
            <h3>Available Test Accounts:</h3>
            <ul>
                <li><strong><EMAIL></strong> / password</li>
                <li><strong><EMAIL></strong> / password123</li>
                <li><strong><EMAIL></strong> / password123</li>
                <li><strong><EMAIL></strong> / password123</li>
                <li><strong><EMAIL></strong> / password123</li>
            </ul>
        </div>

        <form id="debugForm">
            @csrf
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" name="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" name="password" id="password" value="password" required>
            </div>
            
            <button type="submit">Test Login</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        document.getElementById('debugForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('/debug-login', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });
                
                const result = await response.json();
                
                let html = '<div class="result"><h3>🧪 Debug Results:</h3>';
                
                if (result.user_found) {
                    html += '<div class="success">✅ User found: ' + result.user_name + '</div>';
                    
                    if (result.password_correct) {
                        html += '<div class="success">✅ Password is correct</div>';
                        
                        if (result.auth_attempt) {
                            html += '<div class="success">✅ Auth::attempt() successful</div>';
                            html += '<div class="info">🎉 Login should work! Try the actual login page.</div>';
                        } else {
                            html += '<div class="error">❌ Auth::attempt() failed</div>';
                        }
                    } else {
                        html += '<div class="error">❌ Password is incorrect</div>';
                    }
                } else {
                    html += '<div class="error">❌ User not found with email: ' + result.email + '</div>';
                }
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ Error: ' + error.message + '</div>';
            }
        });
        
        // Quick test buttons
        const quickTests = [
            { email: '<EMAIL>', password: 'password' },
            { email: '<EMAIL>', password: 'password123' },
            { email: '<EMAIL>', password: 'password123' }
        ];
        
        quickTests.forEach(test => {
            const button = document.createElement('button');
            button.textContent = `Quick Test: ${test.email}`;
            button.style.margin = '5px';
            button.style.background = '#28a745';
            button.onclick = function() {
                document.getElementById('email').value = test.email;
                document.getElementById('password').value = test.password;
            };
            document.querySelector('.container').appendChild(button);
        });
    </script>
</body>
</html>
