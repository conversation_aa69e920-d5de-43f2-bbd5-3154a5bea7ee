@extends('layouts.main')

@section('title', 'Products - E-Commerce Store')

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title-section">
            <h1 class="page-title">📦 Products</h1>
            <p class="page-subtitle">Discover our amazing collection of quality products</p>
        </div>
        <div class="page-stats">
            <span class="results-count">{{ $products->total() }} products found</span>
        </div>
    </div>

    <div class="products-layout">
        <!-- Filters Sidebar -->
        <aside class="filters-sidebar">
            <div class="filters-header">
                <h3>🔍 Filters</h3>
                <button onclick="clearAllFilters()" class="clear-filters">Clear All</button>
            </div>

            <form method="GET" action="{{ url('/products') }}" id="filtersForm">
                <!-- Search Filter -->
                <div class="filter-group">
                    <label class="filter-label">Search</label>
                    <input type="text" name="search" value="{{ request('search') }}" 
                           placeholder="Search products..." class="filter-input">
                </div>

                <!-- Category Filter -->
                @if($categories->count() > 0)
                <div class="filter-group">
                    <label class="filter-label">Categories</label>
                    <div class="filter-options">
                        @foreach($categories as $category)
                            <label class="filter-option">
                                <input type="radio" name="category" value="{{ $category->id }}" 
                                       {{ request('category') == $category->id ? 'checked' : '' }}>
                                <span class="option-text">{{ $category->name }}</span>
                                <span class="option-count">({{ $category->product_groups_count }})</span>
                            </label>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Tags Filter -->
                @if($tags->count() > 0)
                <div class="filter-group">
                    <label class="filter-label">Tags</label>
                    <div class="filter-options">
                        @foreach($tags->take(10) as $tag)
                            <label class="filter-option">
                                <input type="radio" name="tag" value="{{ $tag->id }}" 
                                       {{ request('tag') == $tag->id ? 'checked' : '' }}>
                                <span class="option-text">{{ $tag->name }}</span>
                                <span class="option-count">({{ $tag->product_groups_count }})</span>
                            </label>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Price Range Filter -->
                @if($priceRange)
                <div class="filter-group">
                    <label class="filter-label">Price Range</label>
                    <div class="price-inputs">
                        <input type="number" name="min_price" value="{{ request('min_price') }}" 
                               placeholder="Min" class="price-input" min="0" step="0.01">
                        <span class="price-separator">-</span>
                        <input type="number" name="max_price" value="{{ request('max_price') }}" 
                               placeholder="Max" class="price-input" min="0" step="0.01">
                    </div>
                    <div class="price-range-info">
                        <small>Range: ${{ number_format($priceRange->min_price, 2) }} - ${{ number_format($priceRange->max_price, 2) }}</small>
                    </div>
                </div>
                @endif

                <!-- Rating Filter -->
                <div class="filter-group">
                    <label class="filter-label">Minimum Rating</label>
                    <div class="rating-options">
                        @for($i = 5; $i >= 1; $i--)
                            <label class="filter-option">
                                <input type="radio" name="rating" value="{{ $i }}" 
                                       {{ request('rating') == $i ? 'checked' : '' }}>
                                <span class="rating-display">
                                    @for($j = 1; $j <= 5; $j++)
                                        <span class="star {{ $j <= $i ? 'filled' : '' }}">⭐</span>
                                    @endfor
                                    <span class="rating-text">{{ $i }}+ stars</span>
                                </span>
                            </label>
                        @endfor
                    </div>
                </div>

                <!-- Trending Filter -->
                <div class="filter-group">
                    <label class="filter-option">
                        <input type="checkbox" name="trending" value="yes" 
                               {{ request('trending') == 'yes' ? 'checked' : '' }}>
                        <span class="option-text">🔥 Trending Only</span>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary filter-apply">Apply Filters</button>
            </form>
        </aside>

        <!-- Products Content -->
        <main class="products-content">
            <!-- Sort and View Options -->
            <div class="products-toolbar">
                <div class="sort-options">
                    <label for="sort">Sort by:</label>
                    <select name="sort" id="sort" onchange="updateSort(this.value)">
                        <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest</option>
                        <option value="price_low" {{ request('sort') == 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') == 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Highest Rated</option>
                        <option value="name" {{ request('sort') == 'name' ? 'selected' : '' }}>Name A-Z</option>
                        <option value="trending" {{ request('sort') == 'trending' ? 'selected' : '' }}>Trending</option>
                    </select>
                </div>
                <div class="view-options">
                    <button onclick="setView('grid')" class="view-btn active" id="grid-view">🔲</button>
                    <button onclick="setView('list')" class="view-btn" id="list-view">📋</button>
                </div>
            </div>

            <!-- Products Grid -->
            @if($products->count() > 0)
                <div class="products-grid" id="productsGrid">
                    @foreach($products as $product)
                        <div class="product-card">
                            <div class="product-image">
                                @if($product->main_image)
                                    <img src="{{ $product->main_image }}" alt="{{ $product->name }}" loading="lazy">
                                @else
                                    <div class="no-image">📦</div>
                                @endif
                                <div class="product-badges">
                                    @if($product->is_trending)
                                        <span class="badge trending">🔥 Trending</span>
                                    @endif
                                </div>
                                <div class="product-overlay">
                                    <button class="quick-view-btn" onclick="quickView({{ $product->id }})">👁️ Quick View</button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-category">{{ $product->category->name }}</div>
                                <h3 class="product-name">
                                    <a href="{{ url('/products/' . $product->id) }}">{{ $product->name }}</a>
                                </h3>
                                <p class="product-description">{{ Str::limit($product->description, 100) }}</p>
                                
                                <!-- Tags -->
                                @if($product->tags->count() > 0)
                                    <div class="product-tags">
                                        @foreach($product->tags->take(3) as $tag)
                                            <span class="tag">{{ $tag->name }}</span>
                                        @endforeach
                                    </div>
                                @endif

                                <div class="product-meta">
                                    <div class="product-price">
                                        <span class="price">${{ number_format($product->base_price, 2) }}</span>
                                        @if($product->products->count() > 1)
                                            <span class="price-range">
                                                - ${{ number_format($product->products->max('price_adjustment') + $product->base_price, 2) }}
                                            </span>
                                        @endif
                                    </div>
                                    @if($product->rating)
                                        <div class="product-rating">
                                            @for($i = 1; $i <= 5; $i++)
                                                <span class="star {{ $i <= $product->rating ? 'filled' : '' }}">⭐</span>
                                            @endfor
                                            <span class="rating-text">({{ number_format($product->rating, 1) }})</span>
                                        </div>
                                    @endif
                                </div>

                                <div class="product-variants">
                                    @if($product->products->count() > 0)
                                        <span class="variants-count">{{ $product->products->count() }} variants</span>
                                        <div class="variant-colors">
                                            @foreach($product->products->unique('color')->take(5) as $variant)
                                                <span class="color-dot" style="background-color: {{ strtolower($variant->color) }}" 
                                                      title="{{ $variant->color }}"></span>
                                            @endforeach
                                            @if($product->products->unique('color')->count() > 5)
                                                <span class="more-colors">+{{ $product->products->unique('color')->count() - 5 }}</span>
                                            @endif
                                        </div>
                                    @endif
                                </div>

                                <div class="product-actions">
                                    <a href="{{ url('/products/' . $product->id) }}" class="btn btn-primary btn-sm">View Details</a>
                                    <button class="btn btn-outline btn-sm" onclick="addToWishlist({{ $product->id }})">❤️</button>
                                    <button class="btn btn-secondary btn-sm" onclick="addToCart({{ $product->id }})">🛒</button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper">
                    {{ $products->withQueryString()->links() }}
                </div>
            @else
                <!-- No Products Found -->
                <div class="no-products">
                    <div class="no-products-icon">📦</div>
                    <h3>No products found</h3>
                    <p>Try adjusting your filters or search terms to find what you're looking for.</p>
                    <button onclick="clearAllFilters()" class="btn btn-primary">Clear All Filters</button>
                </div>
            @endif
        </main>
    </div>
</div>

@push('styles')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .page-subtitle {
        color: #6b7280;
        font-size: 1.125rem;
    }

    .results-count {
        color: #6b7280;
        font-weight: 500;
    }

    .products-layout {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 2rem;
    }

    /* Filters Sidebar */
    .filters-sidebar {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        height: fit-content;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 2rem;
    }

    .filters-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .filters-header h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
    }

    .clear-filters {
        background: none;
        border: none;
        color: #3b82f6;
        font-size: 0.875rem;
        cursor: pointer;
        text-decoration: underline;
    }

    .filter-group {
        margin-bottom: 1.5rem;
    }

    .filter-label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.75rem;
    }

    .filter-input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .filter-options {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-option {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        padding: 0.25rem 0;
    }

    .filter-option input[type="radio"],
    .filter-option input[type="checkbox"] {
        margin: 0;
    }

    .option-text {
        flex: 1;
        font-size: 0.875rem;
        color: #374151;
    }

    .option-count {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .price-inputs {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .price-input {
        flex: 1;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .price-separator {
        color: #6b7280;
    }

    .price-range-info {
        margin-top: 0.5rem;
    }

    .rating-options {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .rating-display {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .star {
        font-size: 0.875rem;
        color: #d1d5db;
    }

    .star.filled {
        color: #fbbf24;
    }

    .rating-text {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .filter-apply {
        width: 100%;
        margin-top: 1rem;
    }

    /* Products Content */
    .products-content {
        min-height: 600px;
    }

    .products-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .sort-options {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .sort-options select {
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .view-options {
        display: flex;
        gap: 0.5rem;
    }

    .view-btn {
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .view-btn.active {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }

    /* Products Grid */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 2rem;
    }

    .product-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .product-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.05);
    }

    .no-image {
        width: 100%;
        height: 100%;
        background: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: #d1d5db;
    }

    .product-badges {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .badge.trending {
        background: #fbbf24;
        color: #92400e;
    }

    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .product-card:hover .product-overlay {
        opacity: 1;
    }

    .quick-view-btn {
        background: white;
        color: #374151;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .quick-view-btn:hover {
        background: #3b82f6;
        color: white;
    }

    .product-info {
        padding: 1.5rem;
    }

    .product-category {
        color: #6b7280;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .product-name {
        margin-bottom: 0.5rem;
    }

    .product-name a {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .product-name a:hover {
        color: #3b82f6;
    }

    .product-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .product-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .tag {
        background: #f3f4f6;
        color: #374151;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .product-price {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .price {
        font-size: 1.25rem;
        font-weight: 700;
        color: #059669;
    }

    .price-range {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .product-rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .product-variants {
        margin-bottom: 1rem;
    }

    .variants-count {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 0.5rem;
        display: block;
    }

    .variant-colors {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .color-dot {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid #e5e7eb;
        cursor: pointer;
    }

    .more-colors {
        font-size: 0.75rem;
        color: #6b7280;
    }

    .product-actions {
        display: flex;
        gap: 0.5rem;
    }

    .product-actions .btn {
        flex: 1;
    }

    /* No Products */
    .no-products {
        text-align: center;
        padding: 4rem 2rem;
        color: #6b7280;
    }

    .no-products-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .no-products h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: #374151;
    }

    .no-products p {
        margin-bottom: 2rem;
        font-size: 1.125rem;
    }

    /* Pagination */
    .pagination-wrapper {
        margin-top: 3rem;
        display: flex;
        justify-content: center;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .products-layout {
            grid-template-columns: 1fr;
        }

        .filters-sidebar {
            position: static;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
        }

        .products-toolbar {
            flex-direction: column;
            gap: 1rem;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function updateSort(value) {
        const url = new URL(window.location);
        url.searchParams.set('sort', value);
        window.location.href = url.toString();
    }

    function setView(view) {
        const gridBtn = document.getElementById('grid-view');
        const listBtn = document.getElementById('list-view');
        const grid = document.getElementById('productsGrid');
        
        if (view === 'grid') {
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
            grid.className = 'products-grid';
        } else {
            gridBtn.classList.remove('active');
            listBtn.classList.add('active');
            grid.className = 'products-list';
        }
    }

    function clearAllFilters() {
        window.location.href = '/products';
    }

    function quickView(productId) {
        // Quick view functionality - could open a modal
        window.location.href = `/products/${productId}`;
    }

    function addToWishlist(productId) {
        @auth
            fetch('/wishlist/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ product_id: productId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Added to wishlist!');
                } else {
                    alert('❌ ' + (data.message || 'Failed to add to wishlist'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Failed to add to wishlist');
            });
        @else
            alert('Please login to add items to your wishlist');
            window.location.href = '/login';
        @endauth
    }

    function addToCart(productId) {
        @auth
            fetch('/cart/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ product_id: productId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Added to cart!');
                    updateCartBadge(data.cart_count);
                } else {
                    alert('❌ ' + (data.message || 'Failed to add to cart'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('❌ Failed to add to cart');
            });
        @else
            alert('Please login to add items to your cart');
            window.location.href = '/login';
        @endauth
    }

    // Auto-submit filters on change
    document.getElementById('filtersForm').addEventListener('change', function() {
        this.submit();
    });
</script>
@endpush
@endsection
